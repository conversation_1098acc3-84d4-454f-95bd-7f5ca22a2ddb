#ifndef WIFI_MANAGER_H
#define WIFI_MANAGER_H

#include "DeviceConfig.h"
// Platform-specific includes are now handled in DeviceConfig.h

class WiFiManager
{
private:
    String _ssid;
    String _password;
    bool _connected;
    int _connectionTimeout;
    int _maxConnectionAttempts;

    // Read string from EEPROM at specified address
    String readStringFromEEPROM(int startAddr, int maxLength)
    {
        char data[maxLength + 1];
        for (int i = 0; i < maxLength; i++)
        {
            data[i] = char(EEPROM.read(startAddr + i));
        }
        data[maxLength] = '\0';
        return String(data);
    }

    // Write string to EEPROM at specified address
    void writeStringToEEPROM(int startAddr, const String &strToWrite, int maxLength)
    {
        for (int i = 0; i < maxLength; i++)
        {
            if (i < strToWrite.length())
            {
                EEPROM.write(startAddr + i, strToWrite[i]);
            }
            else
            {
                EEPROM.write(startAddr + i, 0);
            }
        }
        EEPROM.commit();
    }

public:
    // Always use exactly 3 connection attempts
    WiFiManager(int connectionTimeout = 10000)
        : _connected(false), _connectionTimeout(connectionTimeout), _maxConnectionAttempts(3)
    {
        // Initialize EEPROM
        EEPROM.begin(EEPROM_SIZE);
        Serial.print(DEVICE_TYPE);
        Serial.println(" WiFi Manager EEPROM initialized");
    }

    // Check if credentials are stored in EEPROM
    bool hasStoredCredentials()
    {
        return EEPROM.read(HAS_CREDENTIALS_ADDR) == 1;
    }

    // Load credentials from EEPROM
    bool loadCredentials()
    {
        if (!hasStoredCredentials())
        {
            return false;
        }

        _ssid = readStringFromEEPROM(SSID_ADDR, SSID_SIZE);
        _password = readStringFromEEPROM(PASS_ADDR, PASS_SIZE);

        Serial.print(DEVICE_TYPE);
        Serial.println(" loaded credentials from EEPROM:");
        Serial.print("SSID: ");
        Serial.println(_ssid);

        return true;
    }

    // Save credentials to EEPROM
    void saveCredentials(const String &ssid, const String &password)
    {
        _ssid = ssid;
        _password = password;

        writeStringToEEPROM(SSID_ADDR, _ssid, SSID_SIZE);
        writeStringToEEPROM(PASS_ADDR, _password, PASS_SIZE);
        EEPROM.write(HAS_CREDENTIALS_ADDR, 1);
        EEPROM.commit();

        Serial.print(DEVICE_TYPE);
        Serial.println(" saved credentials to EEPROM");
    }

    // Clear stored credentials
    void clearCredentials()
    {
        EEPROM.write(HAS_CREDENTIALS_ADDR, 0);
        EEPROM.commit();
        _ssid = "";
        _password = "";
        Serial.print(DEVICE_TYPE);
        Serial.println(" cleared stored credentials");
    }

    // Connect to WiFi with stored credentials
    bool connectWithStoredCredentials()
    {
        if (!loadCredentials())
        {
            Serial.print(DEVICE_TYPE);
            Serial.println(": No stored credentials found");
            return false;
        }

        // Make sure _ssid is properly set before connecting
        String ssid = _ssid;
        String password = _password;
        return connect(ssid, password);
    }

    // Connect to WiFi with provided credentials
    bool connect(const String &ssid, const String &password)
    {
        // Use local variables instead of setting member variables until connection is successful
        String tempSsid = ssid;
        String tempPassword = password;

        Serial.print(DEVICE_TYPE);
        Serial.print(" connecting to WiFi: ");
        Serial.println(tempSsid);

        // Always maintain AP+STA mode to keep the webserver accessible
        WiFi.mode(WIFI_MODE_AP_STA);

        // Disconnect first to ensure a clean connection attempt
        if (WiFi.status() == WL_CONNECTED)
        {
            Serial.print(DEVICE_TYPE);
            Serial.println(" disconnecting from current network before connecting to new one");
            WiFi.disconnect(false); // false = don't disable the station
            delay(100);
        }

        WiFi.begin(tempSsid.c_str(), tempPassword.c_str());

        // Always try exactly 3 times before giving up
        for (int attempts = 1; attempts <= _maxConnectionAttempts; attempts++)
        {
            Serial.print(DEVICE_TYPE);
            Serial.print(" attempt ");
            Serial.print(attempts);
            Serial.print(" of ");
            Serial.println(_maxConnectionAttempts);

            unsigned long startTime = millis();
            while (WiFi.status() != WL_CONNECTED && millis() - startTime < _connectionTimeout)
            {
                delay(500);
                Serial.print(".");
            }
            Serial.println();

            if (WiFi.status() == WL_CONNECTED)
            {
                // Only set member variables when connection is successful
                _ssid = tempSsid;
                _password = tempPassword;
                _connected = true;
                Serial.print(DEVICE_TYPE);
                Serial.println(" connected to WiFi!");
                Serial.print("IP address: ");
                Serial.println(WiFi.localIP());
                return true;
            }

            // If this is not the last attempt, try again
            if (attempts < _maxConnectionAttempts)
            {
                Serial.print(DEVICE_TYPE);
                Serial.println(" failed to connect, retrying...");
                WiFi.disconnect(false); // false = don't disable the station
                delay(1000);
                // Make sure we're still in AP+STA mode
                WiFi.mode(WIFI_MODE_AP_STA);
                WiFi.begin(tempSsid.c_str(), tempPassword.c_str());
            }
        }

        _connected = false;
        Serial.print(DEVICE_TYPE);
        Serial.println(" failed to connect to WiFi after 3 attempts");
        Serial.print(DEVICE_TYPE);
        Serial.println(" connection failed - NOT saving credentials");
        // Make sure we don't have any credentials stored for this failed connection
        // This is a safeguard in case something else is saving credentials
        if (_ssid == tempSsid)
        {
            Serial.print(DEVICE_TYPE);
            Serial.println(" clearing any stored credentials for this network");
            clearCredentials();
        }
        return false;
    }

    // Check if connected to WiFi
    bool isConnected()
    {
        return WiFi.status() == WL_CONNECTED;
    }

    // Disconnect from WiFi
    void disconnect()
    {
        WiFi.disconnect();
        _connected = false;
    }

    // Get current SSID
    String getSSID()
    {
        // First try to get the SSID directly from the WiFi library if connected
        if (isConnected())
        {
            String currentSSID = WiFi.SSID();
            if (currentSSID.length() > 0)
            {
                return currentSSID;
            }
        }
        // Fall back to stored SSID
        return _ssid;
    }

    // Get stored password
    String getPassword()
    {
        return _password;
    }

    // Get current IP address
    IPAddress getIP()
    {
        return WiFi.localIP();
    }

    // Non-blocking connect for web interface - just starts the connection
    bool startConnection(const String &ssid, const String &password)
    {
        Serial.print(DEVICE_TYPE);
        Serial.print(" starting connection to WiFi: ");
        Serial.println(ssid);

        // Always maintain AP+STA mode to keep the webserver accessible
        WiFi.mode(WIFI_MODE_AP_STA);

        // Disconnect first to ensure a clean connection attempt
        if (WiFi.status() == WL_CONNECTED)
        {
            Serial.print(DEVICE_TYPE);
            Serial.println(" disconnecting from current network before connecting to new one");
            WiFi.disconnect(false); // false = don't disable the station
        }

        // Start the connection attempt
        WiFi.begin(ssid.c_str(), password.c_str());

        // Store credentials temporarily (will be saved only if connection succeeds)
        _ssid = ssid;
        _password = password;

        return true;
    }

    // Update AP configuration (SSID and password)
    bool updateAPConfig(const String &currentPassword, const String &newSSID, const String &newPassword)
    {
        // Validate new parameters first
        if (newSSID.length() < 1 || newSSID.length() > 32)
        {
            Serial.println("Invalid SSID length");
            return false;
        }

        if (newPassword.length() < 8 || newPassword.length() > 63)
        {
            Serial.println("Invalid password length");
            return false;
        }

        // Save new AP configuration to EEPROM
        writeStringToEEPROM(AP_SSID_ADDR, newSSID, AP_SSID_SIZE);
        writeStringToEEPROM(AP_PASS_ADDR, newPassword, AP_PASS_SIZE);
        EEPROM.commit();

        Serial.println("AP configuration updated in EEPROM");
        Serial.print("New AP SSID: ");
        Serial.println(newSSID);
        Serial.print("New AP Password: ");
        Serial.println(newPassword);

        return true;
    }

    // Load AP configuration from EEPROM
    bool loadAPConfig(String &ssid, String &password)
    {
        ssid = readStringFromEEPROM(AP_SSID_ADDR, AP_SSID_SIZE);
        password = readStringFromEEPROM(AP_PASS_ADDR, AP_PASS_SIZE);

        // If no configuration stored, use defaults
        if (ssid.length() == 0)
        {
            ssid = DEFAULT_AP_SSID;
            password = DEFAULT_AP_PASSWORD;
            return false; // No stored config
        }

        return true; // Config loaded from EEPROM
    }

    // Get current AP password for verification
    String getCurrentAPPassword()
    {
        String password = readStringFromEEPROM(AP_PASS_ADDR, AP_PASS_SIZE);

        // If no stored password, return default
        if (password.length() == 0)
        {
            return DEFAULT_AP_PASSWORD; // Default password
        }

        return password;
    }

    // Initialize AP with stored configuration
    bool initializeAP()
    {
        String apSSID, apPassword;
        bool hasStoredConfig = loadAPConfig(apSSID, apPassword);

        Serial.print("Initializing AP with ");
        Serial.print(hasStoredConfig ? "stored" : "default");
        Serial.println(" configuration:");
        Serial.print("SSID: ");
        Serial.println(apSSID);

        // Start the AP
        bool success = WiFi.softAP(apSSID.c_str(), apPassword.c_str());

        if (success)
        {
            Serial.print("Access Point started successfully - IP: ");
            Serial.println(WiFi.softAPIP());
        }
        else
        {
            Serial.println("Failed to start Access Point");
        }

        return success;
    }
};

#endif // WIFI_MANAGER_H
