#ifndef WEB_TEMPLATES_ESP8266_H
#define WEB_TEMPLATES_ESP8266_H

#include "DeviceConfig.h"

// Only compile ESP8266 templates for ESP8266 devices
#ifndef IS_ESP32

// ESP8266 Switch Control Template (Basic RGB)
const char ESP8266_switchesTemplate[] PROGMEM = R"=====(
<div class='content-section'>
<h1>Switch Control</h1>
<div class='switch-grid' id='switchGrid'>
<div class='loading-message'>Loading switches...</div>
</div>
</div>
<script>
function toggleSwitch(i){fetch('/switch',{method:'POST',headers:{'Content-Type':'application/x-www-form-urlencoded'},body:'index='+i+'&state='+(document.querySelector('.switch-card:nth-child('+(i+1)+') .switch-state').classList.contains('on')?'0':'1')}).then(()=>{console.log('Switch toggle sent for index:', i);}).catch(e=>console.error('Toggle switch error:', e));}

function setRGBColor(switchIndex, state, color) {
    const endpoint = state === 'off' ? '/rgb/off' : '/rgb/on';
    fetch(endpoint, {
        method: 'POST',
        headers: {'Content-Type': 'application/x-www-form-urlencoded'},
        body: 'index=' + switchIndex + '&color=' + color
    }).then(response => {
        if (response.ok) {
            console.log('RGB color set for switch', switchIndex, 'state', state, 'to', color);
            // Update dropdown visual
            const dropdown = document.querySelector('.switch-card:nth-child(' + (switchIndex + 1) + ') .color-dropdown.' + state);
            if (dropdown) {
                dropdown.className = 'color-dropdown ' + state + ' ' + color;
            }
        }
    }).catch(e => console.error('RGB color error:', e));
}

function loadInitialSwitches(){
fetch('/api/status').then(r=>{
if(!r.ok) throw new Error('Status API failed: ' + r.status);
return r.json();
}).then(d=>{
let html = '';
for(let i = 0; i < d.switchCount; i++){
const state = d.switchState[i];
const offColor = d.rgbOffColors ? d.rgbOffColors[i] : 'off';
const onColor = d.rgbOnColors ? d.rgbOnColors[i] : 'off';
html += '<div class="switch-card"><div class="switch-header"><div class="switch-title">Switch ' + (i+1) + '</div>';
html += '<div class="switch-state ' + (state ? 'on' : 'off') + '">' + (state ? 'ON' : 'OFF') + '</div></div>';
html += '<div class="toggle-container"><span>Toggle Switch</span>';
html += '<div class="toggle-switch ' + (state ? 'on' : '') + '" onclick="toggleSwitch(' + i + ')"></div></div>';
html += '<div class="color-controls"><div class="color-group"><h4>OFF Color</h4><div class="color-selector">';
html += '<select class="color-dropdown off ' + offColor + '" onchange="setRGBColor(' + i + ', \'off\', this.value)">';
html += '<option value="off"' + (offColor === 'off' ? ' selected' : '') + '>Off</option>';
html += '<option value="red"' + (offColor === 'red' ? ' selected' : '') + '>Red</option>';
html += '<option value="green"' + (offColor === 'green' ? ' selected' : '') + '>Green</option>';
html += '<option value="blue"' + (offColor === 'blue' ? ' selected' : '') + '>Blue</option>';
html += '<option value="yellow"' + (offColor === 'yellow' ? ' selected' : '') + '>Yellow</option>';
html += '<option value="cyan"' + (offColor === 'cyan' ? ' selected' : '') + '>Cyan</option>';
html += '<option value="magenta"' + (offColor === 'magenta' ? ' selected' : '') + '>Magenta</option>';
html += '<option value="white"' + (offColor === 'white' ? ' selected' : '') + '>White</option></select></div></div>';
html += '<div class="color-group"><h4>ON Color</h4><div class="color-selector">';
html += '<select class="color-dropdown on ' + onColor + '" onchange="setRGBColor(' + i + ', \'on\', this.value)">';
html += '<option value="off"' + (onColor === 'off' ? ' selected' : '') + '>Off</option>';
html += '<option value="red"' + (onColor === 'red' ? ' selected' : '') + '>Red</option>';
html += '<option value="green"' + (onColor === 'green' ? ' selected' : '') + '>Green</option>';
html += '<option value="blue"' + (onColor === 'blue' ? ' selected' : '') + '>Blue</option>';
html += '<option value="yellow"' + (onColor === 'yellow' ? ' selected' : '') + '>Yellow</option>';
html += '<option value="cyan"' + (onColor === 'cyan' ? ' selected' : '') + '>Cyan</option>';
html += '<option value="magenta"' + (onColor === 'magenta' ? ' selected' : '') + '>Magenta</option>';
html += '<option value="white"' + (onColor === 'white' ? ' selected' : '') + '>White</option></select></div></div></div></div>';
}
document.getElementById('switchGrid').innerHTML = html;
}).catch(e=>{
console.error('Load switches error:', e);
document.getElementById('switchGrid').innerHTML = '<div class="error-message">Failed to load switches</div>';
});
}

// SSE for real-time updates
function setupSwitchSSE() {
    const eventSource = new EventSource('/api/switches/events');
    eventSource.onmessage = function(event) {
        try {
            const data = JSON.parse(event.data);
            updateSwitchDisplay(data);
        } catch (e) {
            console.error('SSE parse error:', e);
        }
    };
    eventSource.onerror = function(event) {
        console.log('SSE connection error, retrying...');
        setTimeout(() => {
            eventSource.close();
            setupSwitchSSE();
        }, 5000);
    };
}

function updateSwitchDisplay(data) {
    for (let i = 0; i < data.switchCount; i++) {
        const switchCard = document.querySelector('.switch-card:nth-child(' + (i + 1) + ')');
        if (switchCard) {
            const stateElement = switchCard.querySelector('.switch-state');
            const toggleElement = switchCard.querySelector('.toggle-switch');
            
            if (data.switchState[i]) {
                stateElement.textContent = 'ON';
                stateElement.className = 'switch-state on';
                toggleElement.classList.add('on');
            } else {
                stateElement.textContent = 'OFF';
                stateElement.className = 'switch-state off';
                toggleElement.classList.remove('on');
            }
        }
    }
}

window.addEventListener('load', function() {
    setActiveNav('/switches');
    loadInitialSwitches();
    setupSwitchSSE();
});
</script>
)=====";

// ESP8266 Scenario Key Template (No relays, RGB only)
const char ESP8266_scenarioTemplate[] PROGMEM = R"=====(
<div class='content-section'>
<h1>Scenario Key Control</h1>
<div class='switch-grid' id='switchGrid'>
<div class='loading-message'>Loading scenario keys...</div>
</div>
</div>
<script>
function setRGBColor(switchIndex, state, color) {
    const endpoint = state === 'off' ? '/rgb/off' : '/rgb/on';
    fetch(endpoint, {
        method: 'POST',
        headers: {'Content-Type': 'application/x-www-form-urlencoded'},
        body: 'index=' + switchIndex + '&color=' + color
    }).then(response => {
        if (response.ok) {
            console.log('RGB color set for key', switchIndex, 'state', state, 'to', color);
            const dropdown = document.querySelector('.switch-card:nth-child(' + (switchIndex + 1) + ') .color-dropdown.' + state);
            if (dropdown) {
                dropdown.className = 'color-dropdown ' + state + ' ' + color;
            }
        }
    }).catch(e => console.error('RGB color error:', e));
}

function loadInitialSwitches(){
fetch('/api/status').then(r=>{
if(!r.ok) throw new Error('Status API failed: ' + r.status);
return r.json();
}).then(d=>{
let html = '';
for(let i = 0; i < d.switchCount; i++){
const offColor = d.rgbOffColors ? d.rgbOffColors[i] : 'off';
const onColor = d.rgbOnColors ? d.rgbOnColors[i] : 'off';
html += '<div class="switch-card"><div class="switch-header"><div class="switch-title">Key ' + (i+1) + '</div>';
html += '<div class="switch-state scenario-key">SCENARIO</div></div>';
// No toggle switch for scenario keys
html += '<div class="color-controls"><div class="color-group"><h4>Idle Color</h4><div class="color-selector">';
html += '<select class="color-dropdown off ' + offColor + '" onchange="setRGBColor(' + i + ', \'off\', this.value)">';
html += '<option value="off"' + (offColor === 'off' ? ' selected' : '') + '>Off</option>';
html += '<option value="red"' + (offColor === 'red' ? ' selected' : '') + '>Red</option>';
html += '<option value="green"' + (offColor === 'green' ? ' selected' : '') + '>Green</option>';
html += '<option value="blue"' + (offColor === 'blue' ? ' selected' : '') + '>Blue</option>';
html += '<option value="yellow"' + (offColor === 'yellow' ? ' selected' : '') + '>Yellow</option>';
html += '<option value="cyan"' + (offColor === 'cyan' ? ' selected' : '') + '>Cyan</option>';
html += '<option value="magenta"' + (offColor === 'magenta' ? ' selected' : '') + '>Magenta</option>';
html += '<option value="white"' + (offColor === 'white' ? ' selected' : '') + '>White</option></select></div></div>';
html += '<div class="color-group"><h4>Active Color</h4><div class="color-selector">';
html += '<select class="color-dropdown on ' + onColor + '" onchange="setRGBColor(' + i + ', \'on\', this.value)">';
html += '<option value="off"' + (onColor === 'off' ? ' selected' : '') + '>Off</option>';
html += '<option value="red"' + (onColor === 'red' ? ' selected' : '') + '>Red</option>';
html += '<option value="green"' + (onColor === 'green' ? ' selected' : '') + '>Green</option>';
html += '<option value="blue"' + (onColor === 'blue' ? ' selected' : '') + '>Blue</option>';
html += '<option value="yellow"' + (onColor === 'yellow' ? ' selected' : '') + '>Yellow</option>';
html += '<option value="cyan"' + (onColor === 'cyan' ? ' selected' : '') + '>Cyan</option>';
html += '<option value="magenta"' + (onColor === 'magenta' ? ' selected' : '') + '>Magenta</option>';
html += '<option value="white"' + (onColor === 'white' ? ' selected' : '') + '>White</option></select></div></div></div></div>';
}
document.getElementById('switchGrid').innerHTML = html;
}).catch(e=>{
console.error('Load scenario keys error:', e);
document.getElementById('switchGrid').innerHTML = '<div class="error-message">Failed to load scenario keys</div>';
});
}

window.addEventListener('load', function() {
    setActiveNav('/switches');
    loadInitialSwitches();
});
</script>
)=====";

// ESP8266 CSS additions for basic RGB controls
const char ESP8266_switchesCSS[] PROGMEM = R"=====(
.color-controls {
    margin-top: 15px;
    display: flex;
    gap: 15px;
    flex-wrap: wrap;
}

.color-group {
    flex: 1;
    min-width: 120px;
}

.color-group h4 {
    margin: 0 0 8px 0;
    font-size: 14px;
    color: #666;
    font-weight: 500;
}

.color-selector {
    position: relative;
}

.color-dropdown {
    width: 100%;
    padding: 8px 12px;
    border: 2px solid #ddd;
    border-radius: 6px;
    background: #fff;
    font-size: 14px;
    cursor: pointer;
    transition: all 0.3s ease;
}

.color-dropdown:focus {
    outline: none;
    border-color: #007bff;
    box-shadow: 0 0 0 3px rgba(0, 123, 255, 0.1);
}

/* Color-specific styling for dropdowns */
.color-dropdown.red { border-color: #dc3545; background-color: #fff5f5; }
.color-dropdown.green { border-color: #28a745; background-color: #f5fff5; }
.color-dropdown.blue { border-color: #007bff; background-color: #f5f9ff; }
.color-dropdown.yellow { border-color: #ffc107; background-color: #fffef5; }
.color-dropdown.cyan { border-color: #17a2b8; background-color: #f5feff; }
.color-dropdown.magenta { border-color: #e83e8c; background-color: #fff5fc; }
.color-dropdown.white { border-color: #6c757d; background-color: #f8f9fa; }
.color-dropdown.off { border-color: #6c757d; background-color: #f8f9fa; }

.scenario-key {
    background: linear-gradient(135deg, #6c5ce7, #a29bfe);
    color: white;
}

@media (max-width: 768px) {
    .color-controls {
        flex-direction: column;
        gap: 10px;
    }
    
    .color-group {
        min-width: auto;
    }
}
)=====";

#endif // !IS_ESP32

#endif // WEB_TEMPLATES_ESP8266_H
