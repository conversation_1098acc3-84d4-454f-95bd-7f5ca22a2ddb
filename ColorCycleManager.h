#ifndef COLOR_CYCLE_MANAGER_H
#define COLOR_CYCLE_MANAGER_H

#include "DeviceConfig.h"

// Only compile this manager for devices with color cycle capability
#ifdef COMPILE_COLOR_CYCLE

#include "FullColorRGBManager.h"

class ColorCycleManager
{
public:
    // Color cycle modes
    enum CycleMode
    {
        CYCLE_OFF = 0,
        CYCLE_RAINBOW = 1,
        CYCLE_BREATHING = 2,
        CYCLE_STROBE = 3,
        CYCLE_FADE = 4,
        CYCLE_WAVE = 5,
        CYCLE_FIRE = 6,
        CYCLE_OCEAN = 7,
        CYCLE_CUSTOM = 8
    };
    
    // Cycle parameters
    struct CycleParams
    {
        CycleMode mode;
        uint16_t speed;        // 1-100 (higher = faster)
        uint8_t brightness;    // 0-255
        uint8_t saturation;    // 0-100 (for HSV modes)
        bool enabled;
        uint8_t switchMask;    // Bitmask of which switches to affect
    };

private:
    FullColorRGBManager* _rgbManager;
    uint8_t _switchCount;
    
    // Cycle state
    CycleParams _cycleParams;
    unsigned long _lastUpdateTime;
    uint16_t _cyclePosition;     // 0-359 for hue cycling
    uint8_t _breathingDirection; // 0=up, 1=down
    uint8_t _breathingValue;     // Current breathing brightness
    bool _strobeState;
    unsigned long _strobeLastToggle;
    
    // Wave effect state
    uint16_t _wavePositions[MAX_SWITCHES];
    
    // Fire effect state
    uint8_t _fireIntensity[MAX_SWITCHES];
    unsigned long _fireLastUpdate[MAX_SWITCHES];
    
    // Custom color sequence
    struct CustomColor
    {
        uint8_t r, g, b;
        uint16_t duration; // milliseconds
    };
    
    static const uint8_t MAX_CUSTOM_COLORS = 10;
    CustomColor _customSequence[MAX_CUSTOM_COLORS];
    uint8_t _customColorCount;
    uint8_t _customCurrentIndex;
    unsigned long _customStartTime;
    
    // Convert HSV to RGB (0-360, 0-100, 0-100)
    void hsvToRgb(uint16_t h, uint8_t s, uint8_t v, uint8_t& r, uint8_t& g, uint8_t& b)
    {
        float hf = h / 60.0;
        float sf = s / 100.0;
        float vf = v / 100.0;
        
        int i = (int)hf;
        float f = hf - i;
        float p = vf * (1 - sf);
        float q = vf * (1 - sf * f);
        float t = vf * (1 - sf * (1 - f));
        
        float rf, gf, bf;
        
        switch (i % 6)
        {
            case 0: rf = vf; gf = t; bf = p; break;
            case 1: rf = q; gf = vf; bf = p; break;
            case 2: rf = p; gf = vf; bf = t; break;
            case 3: rf = p; gf = q; bf = vf; break;
            case 4: rf = t; gf = p; bf = vf; break;
            case 5: rf = vf; gf = p; bf = q; break;
            default: rf = gf = bf = 0; break;
        }
        
        r = (uint8_t)(rf * 255);
        g = (uint8_t)(gf * 255);
        b = (uint8_t)(bf * 255);
    }
    
    // Apply brightness to RGB values
    void applyBrightness(uint8_t& r, uint8_t& g, uint8_t& b, uint8_t brightness)
    {
        if (brightness == 255) return;
        
        r = (r * brightness) / 255;
        g = (g * brightness) / 255;
        b = (b * brightness) / 255;
    }
    
    // Update rainbow cycle
    void updateRainbowCycle()
    {
        for (int i = 0; i < _switchCount; i++)
        {
            if (!(_cycleParams.switchMask & (1 << i))) continue;
            
            // Each switch gets a different hue offset for wave effect
            uint16_t hue = (_cyclePosition + (i * 60)) % 360;
            
            uint8_t r, g, b;
            hsvToRgb(hue, _cycleParams.saturation, 100, r, g, b);
            applyBrightness(r, g, b, _cycleParams.brightness);
            
            _rgbManager->setColor(i, r, g, b, true);
        }
        
        // Advance position based on speed
        _cyclePosition = (_cyclePosition + _cycleParams.speed / 10) % 360;
    }
    
    // Update breathing effect
    void updateBreathingCycle()
    {
        // Calculate breathing brightness
        if (_breathingDirection == 0) // Going up
        {
            _breathingValue += _cycleParams.speed / 5;
            if (_breathingValue >= 255)
            {
                _breathingValue = 255;
                _breathingDirection = 1;
            }
        }
        else // Going down
        {
            _breathingValue -= _cycleParams.speed / 5;
            if (_breathingValue <= 0)
            {
                _breathingValue = 0;
                _breathingDirection = 0;
            }
        }
        
        // Apply to all enabled switches
        for (int i = 0; i < _switchCount; i++)
        {
            if (!(_cycleParams.switchMask & (1 << i))) continue;
            
            uint8_t r, g, b;
            hsvToRgb(_cyclePosition, _cycleParams.saturation, 100, r, g, b);
            
            uint8_t finalBrightness = (_cycleParams.brightness * _breathingValue) / 255;
            applyBrightness(r, g, b, finalBrightness);
            
            _rgbManager->setColor(i, r, g, b, true);
        }
    }
    
    // Update strobe effect
    void updateStrobeCycle()
    {
        unsigned long now = millis();
        unsigned long strobeInterval = 1000 / (_cycleParams.speed * 2); // Speed affects strobe rate
        
        if (now - _strobeLastToggle >= strobeInterval)
        {
            _strobeState = !_strobeState;
            _strobeLastToggle = now;
            
            for (int i = 0; i < _switchCount; i++)
            {
                if (!(_cycleParams.switchMask & (1 << i))) continue;
                
                if (_strobeState)
                {
                    uint8_t r, g, b;
                    hsvToRgb(_cyclePosition, _cycleParams.saturation, 100, r, g, b);
                    applyBrightness(r, g, b, _cycleParams.brightness);
                    _rgbManager->setColor(i, r, g, b, true);
                }
                else
                {
                    _rgbManager->setColor(i, 0, 0, 0, true);
                }
            }
        }
    }
    
    // Update wave effect
    void updateWaveCycle()
    {
        for (int i = 0; i < _switchCount; i++)
        {
            if (!(_cycleParams.switchMask & (1 << i))) continue;
            
            // Each switch has its own wave position
            _wavePositions[i] = (_wavePositions[i] + _cycleParams.speed / 5) % 360;
            
            // Calculate wave intensity using sine wave
            float waveIntensity = (sin(_wavePositions[i] * PI / 180.0) + 1.0) / 2.0; // 0-1
            
            uint8_t r, g, b;
            hsvToRgb((_cyclePosition + i * 30) % 360, _cycleParams.saturation, 100, r, g, b);
            
            uint8_t finalBrightness = (uint8_t)(_cycleParams.brightness * waveIntensity);
            applyBrightness(r, g, b, finalBrightness);
            
            _rgbManager->setColor(i, r, g, b, true);
        }
        
        _cyclePosition = (_cyclePosition + 1) % 360;
    }
    
    // Update fire effect
    void updateFireCycle()
    {
        unsigned long now = millis();
        
        for (int i = 0; i < _switchCount; i++)
        {
            if (!(_cycleParams.switchMask & (1 << i))) continue;
            
            // Update fire intensity randomly
            if (now - _fireLastUpdate[i] > (100 - _cycleParams.speed))
            {
                _fireIntensity[i] = random(50, 255);
                _fireLastUpdate[i] = now;
            }
            
            // Fire colors: red-orange-yellow spectrum
            uint8_t hue = random(0, 60); // Red to yellow range
            uint8_t r, g, b;
            hsvToRgb(hue, 100, 100, r, g, b);
            
            uint8_t finalBrightness = (_cycleParams.brightness * _fireIntensity[i]) / 255;
            applyBrightness(r, g, b, finalBrightness);
            
            _rgbManager->setColor(i, r, g, b, true);
        }
    }
    
    // Update ocean effect
    void updateOceanCycle()
    {
        for (int i = 0; i < _switchCount; i++)
        {
            if (!(_cycleParams.switchMask & (1 << i))) continue;
            
            // Ocean colors: blue-cyan-green spectrum
            uint16_t baseHue = 180 + random(-30, 30); // Blue-cyan range with variation
            uint8_t intensity = 128 + (sin((_cyclePosition + i * 45) * PI / 180.0) * 127);
            
            uint8_t r, g, b;
            hsvToRgb(baseHue, _cycleParams.saturation, 100, r, g, b);
            
            uint8_t finalBrightness = (_cycleParams.brightness * intensity) / 255;
            applyBrightness(r, g, b, finalBrightness);
            
            _rgbManager->setColor(i, r, g, b, true);
        }
        
        _cyclePosition = (_cyclePosition + _cycleParams.speed / 10) % 360;
    }
    
    // Update custom sequence
    void updateCustomCycle()
    {
        if (_customColorCount == 0) return;
        
        unsigned long elapsed = millis() - _customStartTime;
        CustomColor& currentColor = _customSequence[_customCurrentIndex];
        
        if (elapsed >= currentColor.duration)
        {
            // Move to next color
            _customCurrentIndex = (_customCurrentIndex + 1) % _customColorCount;
            _customStartTime = millis();
            currentColor = _customSequence[_customCurrentIndex];
        }
        
        // Apply current color to all enabled switches
        for (int i = 0; i < _switchCount; i++)
        {
            if (!(_cycleParams.switchMask & (1 << i))) continue;
            
            uint8_t r = currentColor.r;
            uint8_t g = currentColor.g;
            uint8_t b = currentColor.b;
            applyBrightness(r, g, b, _cycleParams.brightness);
            
            _rgbManager->setColor(i, r, g, b, true);
        }
    }

public:
    ColorCycleManager(FullColorRGBManager* rgbManager, uint8_t switchCount = SWITCH_COUNT)
        : _rgbManager(rgbManager), _switchCount(switchCount), _lastUpdateTime(0),
          _cyclePosition(0), _breathingDirection(0), _breathingValue(0),
          _strobeState(false), _strobeLastToggle(0), _customColorCount(0),
          _customCurrentIndex(0), _customStartTime(0)
    {
        // Initialize default cycle parameters
        _cycleParams.mode = CYCLE_OFF;
        _cycleParams.speed = 50;
        _cycleParams.brightness = 255;
        _cycleParams.saturation = 100;
        _cycleParams.enabled = false;
        _cycleParams.switchMask = 0xFF; // All switches enabled by default
        
        // Initialize wave positions
        for (int i = 0; i < MAX_SWITCHES; i++)
        {
            _wavePositions[i] = i * 60; // Stagger initial positions
            _fireIntensity[i] = 128;
            _fireLastUpdate[i] = 0;
        }
        
        Serial.print(DEVICE_TYPE);
        Serial.println(" ColorCycleManager constructor");
    }
    
    // Initialize color cycling
    void begin()
    {
        Serial.print(DEVICE_TYPE);
        Serial.println(" Initializing Color Cycle Manager...");
        
        if (!_rgbManager)
        {
            Serial.println("ERROR: RGB Manager not provided to ColorCycleManager");
            return;
        }
        
        Serial.print(DEVICE_TYPE);
        Serial.println(" Color Cycle Manager initialized");
        Serial.print("Available modes: Rainbow, Breathing, Strobe, Fade, Wave, Fire, Ocean, Custom");
    }
    
    // Set cycle mode
    void setCycleMode(CycleMode mode)
    {
        _cycleParams.mode = mode;
        _cycleParams.enabled = (mode != CYCLE_OFF);
        
        // Reset cycle state
        _cyclePosition = 0;
        _breathingDirection = 0;
        _breathingValue = 0;
        _strobeState = false;
        _customStartTime = millis();
        
        const char* modeNames[] = {"OFF", "RAINBOW", "BREATHING", "STROBE", "FADE", "WAVE", "FIRE", "OCEAN", "CUSTOM"};
        
        Serial.print(DEVICE_TYPE);
        Serial.print(" Color cycle mode set to ");
        Serial.println(modeNames[mode]);
        
        if (mode == CYCLE_OFF)
        {
            // Turn off all RGB when disabling
            for (int i = 0; i < _switchCount; i++)
            {
                if (_cycleParams.switchMask & (1 << i))
                {
                    _rgbManager->setColor(i, 0, 0, 0, true);
                }
            }
        }
    }
    
    // Set cycle parameters
    void setCycleSpeed(uint16_t speed)
    {
        _cycleParams.speed = constrain(speed, 1, 100);
        Serial.print(DEVICE_TYPE);
        Serial.print(" Color cycle speed set to ");
        Serial.println(_cycleParams.speed);
    }
    
    void setCycleBrightness(uint8_t brightness)
    {
        _cycleParams.brightness = brightness;
        Serial.print(DEVICE_TYPE);
        Serial.print(" Color cycle brightness set to ");
        Serial.print((brightness * 100) / 255);
        Serial.println("%");
    }
    
    void setCycleSaturation(uint8_t saturation)
    {
        _cycleParams.saturation = constrain(saturation, 0, 100);
        Serial.print(DEVICE_TYPE);
        Serial.print(" Color cycle saturation set to ");
        Serial.print(_cycleParams.saturation);
        Serial.println("%");
    }
    
    // Set which switches are affected by cycling
    void setSwitchMask(uint8_t mask)
    {
        _cycleParams.switchMask = mask;
        Serial.print(DEVICE_TYPE);
        Serial.print(" Color cycle switch mask set to 0b");
        Serial.println(mask, BIN);
    }
    
    // Add custom color to sequence
    void addCustomColor(uint8_t r, uint8_t g, uint8_t b, uint16_t duration)
    {
        if (_customColorCount < MAX_CUSTOM_COLORS)
        {
            _customSequence[_customColorCount] = {r, g, b, duration};
            _customColorCount++;
            
            Serial.print(DEVICE_TYPE);
            Serial.print(" Added custom color ");
            Serial.print(_customColorCount);
            Serial.print(": R=");
            Serial.print(r);
            Serial.print(" G=");
            Serial.print(g);
            Serial.print(" B=");
            Serial.print(b);
            Serial.print(" Duration=");
            Serial.print(duration);
            Serial.println("ms");
        }
    }
    
    // Clear custom color sequence
    void clearCustomSequence()
    {
        _customColorCount = 0;
        _customCurrentIndex = 0;
        Serial.print(DEVICE_TYPE);
        Serial.println(" Custom color sequence cleared");
    }
    
    // Main update method - call this in loop()
    void update()
    {
        if (!_cycleParams.enabled || _cycleParams.mode == CYCLE_OFF)
            return;
            
        unsigned long now = millis();
        unsigned long updateInterval = 100 - _cycleParams.speed; // Faster speed = shorter interval
        
        if (now - _lastUpdateTime < updateInterval)
            return;
            
        _lastUpdateTime = now;
        
        switch (_cycleParams.mode)
        {
            case CYCLE_RAINBOW:
                updateRainbowCycle();
                break;
            case CYCLE_BREATHING:
                updateBreathingCycle();
                break;
            case CYCLE_STROBE:
                updateStrobeCycle();
                break;
            case CYCLE_WAVE:
                updateWaveCycle();
                break;
            case CYCLE_FIRE:
                updateFireCycle();
                break;
            case CYCLE_OCEAN:
                updateOceanCycle();
                break;
            case CYCLE_CUSTOM:
                updateCustomCycle();
                break;
            default:
                break;
        }
    }
    
    // Get current status
    String getStatus()
    {
        const char* modeNames[] = {"OFF", "RAINBOW", "BREATHING", "STROBE", "FADE", "WAVE", "FIRE", "OCEAN", "CUSTOM"};
        
        String status = "Mode: " + String(modeNames[_cycleParams.mode]);
        status += ", Speed: " + String(_cycleParams.speed);
        status += ", Brightness: " + String((_cycleParams.brightness * 100) / 255) + "%";
        status += ", Saturation: " + String(_cycleParams.saturation) + "%";
        status += ", Switches: 0b" + String(_cycleParams.switchMask, BIN);
        
        return status;
    }
    
    // Check if cycling is active
    bool isActive()
    {
        return _cycleParams.enabled && _cycleParams.mode != CYCLE_OFF;
    }
};

#endif // COMPILE_COLOR_CYCLE

#endif // COLOR_CYCLE_MANAGER_H
