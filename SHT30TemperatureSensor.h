#ifndef SHT30_TEMPERATURE_SENSOR_H
#define SHT30_TEMPERATURE_SENSOR_H

#include "DeviceConfig.h"

// Only compile this library for devices with temperature sensor
#ifdef COMPILE_TEMPERATURE_SENSOR

#include <Wire.h>

class SHT30TemperatureSensor
{
private:
    // SHT30 I2C address (default: 0x44)
    static const uint8_t SHT30_ADDRESS = 0x44;
    
    // SHT30 commands
    static const uint16_t SHT30_CMD_MEASURE_HIGH_REP = 0x2C06;
    static const uint16_t SHT30_CMD_MEASURE_MED_REP = 0x2C0D;
    static const uint16_t SHT30_CMD_MEASURE_LOW_REP = 0x2C10;
    static const uint16_t SHT30_CMD_SOFT_RESET = 0x30A2;
    static const uint16_t SHT30_CMD_STATUS = 0xF32D;
    
    uint8_t _sdaPin;
    uint8_t _sclPin;
    bool _initialized;
    
    // Last readings
    float _lastTemperature;
    float _lastHumidity;
    unsigned long _lastReadTime;
    
    // Reading interval to avoid overwhelming the sensor
    static const unsigned long READ_INTERVAL = 2000; // 2 seconds
    
    // Send command to SHT30
    bool sendCommand(uint16_t command)
    {
        Wire.beginTransmission(SHT30_ADDRESS);
        Wire.write(command >> 8);   // MSB
        Wire.write(command & 0xFF); // LSB
        return Wire.endTransmission() == 0;
    }
    
    // Calculate CRC8 checksum
    uint8_t calculateCRC(uint8_t data[], uint8_t len)
    {
        uint8_t crc = 0xFF;
        for (uint8_t i = 0; i < len; i++)
        {
            crc ^= data[i];
            for (uint8_t bit = 8; bit > 0; --bit)
            {
                if (crc & 0x80)
                    crc = (crc << 1) ^ 0x31;
                else
                    crc = (crc << 1);
            }
        }
        return crc;
    }
    
    // Verify CRC checksum
    bool verifyCRC(uint8_t data[], uint8_t checksum)
    {
        return calculateCRC(data, 2) == checksum;
    }

public:
    SHT30TemperatureSensor() 
        : _sdaPin(TEMP_SENSOR_SDA_PIN), _sclPin(TEMP_SENSOR_SCL_PIN),
          _initialized(false), _lastTemperature(0.0), _lastHumidity(0.0), _lastReadTime(0)
    {
        Serial.print(DEVICE_TYPE);
        Serial.println(" SHT30 Temperature Sensor constructor");
    }
    
    // Initialize the sensor
    bool begin()
    {
        Serial.print(DEVICE_TYPE);
        Serial.println(" Initializing SHT30 temperature sensor...");
        
        // Initialize I2C with custom pins
        Wire.begin(_sdaPin, _sclPin);
        Wire.setClock(100000); // 100kHz for SHT30
        
        // Soft reset the sensor
        if (!sendCommand(SHT30_CMD_SOFT_RESET))
        {
            Serial.println("ERROR: Failed to send reset command to SHT30");
            return false;
        }
        
        delay(50); // Wait for reset to complete
        
        // Test communication by reading status
        if (!sendCommand(SHT30_CMD_STATUS))
        {
            Serial.println("ERROR: Failed to communicate with SHT30");
            return false;
        }
        
        // Read status response
        if (Wire.requestFrom(SHT30_ADDRESS, (uint8_t)3) != 3)
        {
            Serial.println("ERROR: SHT30 status read failed");
            return false;
        }
        
        uint8_t statusData[3];
        for (int i = 0; i < 3; i++)
        {
            statusData[i] = Wire.read();
        }
        
        // Verify status CRC
        if (!verifyCRC(statusData, statusData[2]))
        {
            Serial.println("WARNING: SHT30 status CRC mismatch");
        }
        
        _initialized = true;
        Serial.print(DEVICE_TYPE);
        Serial.println(" SHT30 temperature sensor initialized successfully");
        Serial.print("SDA Pin: ");
        Serial.print(_sdaPin);
        Serial.print(", SCL Pin: ");
        Serial.println(_sclPin);
        
        return true;
    }
    
    // Read temperature and humidity
    bool readSensor()
    {
        if (!_initialized)
        {
            Serial.println("ERROR: SHT30 not initialized");
            return false;
        }
        
        // Check if enough time has passed since last reading
        unsigned long now = millis();
        if (now - _lastReadTime < READ_INTERVAL)
        {
            return true; // Use cached values
        }
        
        // Send measurement command (high repeatability)
        if (!sendCommand(SHT30_CMD_MEASURE_HIGH_REP))
        {
            Serial.println("ERROR: Failed to send measurement command to SHT30");
            return false;
        }
        
        delay(20); // Wait for measurement to complete
        
        // Read 6 bytes (temp + humidity + CRCs)
        if (Wire.requestFrom(SHT30_ADDRESS, (uint8_t)6) != 6)
        {
            Serial.println("ERROR: SHT30 measurement read failed");
            return false;
        }
        
        uint8_t data[6];
        for (int i = 0; i < 6; i++)
        {
            data[i] = Wire.read();
        }
        
        // Verify temperature CRC
        if (!verifyCRC(&data[0], data[2]))
        {
            Serial.println("ERROR: SHT30 temperature CRC mismatch");
            return false;
        }
        
        // Verify humidity CRC
        if (!verifyCRC(&data[3], data[5]))
        {
            Serial.println("ERROR: SHT30 humidity CRC mismatch");
            return false;
        }
        
        // Convert raw data to temperature (Celsius)
        uint16_t tempRaw = (data[0] << 8) | data[1];
        _lastTemperature = -45.0 + 175.0 * (tempRaw / 65535.0);
        
        // Convert raw data to humidity (%)
        uint16_t humRaw = (data[3] << 8) | data[4];
        _lastHumidity = 100.0 * (humRaw / 65535.0);
        
        _lastReadTime = now;
        
        return true;
    }
    
    // Get temperature in Celsius
    float getTemperature()
    {
        readSensor(); // Update if needed
        return _lastTemperature;
    }
    
    // Get temperature in Fahrenheit
    float getTemperatureFahrenheit()
    {
        return getTemperature() * 9.0 / 5.0 + 32.0;
    }
    
    // Get humidity percentage
    float getHumidity()
    {
        readSensor(); // Update if needed
        return _lastHumidity;
    }
    
    // Get both values at once
    bool getTemperatureAndHumidity(float &temperature, float &humidity)
    {
        if (readSensor())
        {
            temperature = _lastTemperature;
            humidity = _lastHumidity;
            return true;
        }
        return false;
    }
    
    // Check if sensor is responding
    bool isConnected()
    {
        if (!_initialized) return false;
        
        return sendCommand(SHT30_CMD_STATUS);
    }
    
    // Get formatted sensor reading as string
    String getReadingString()
    {
        if (readSensor())
        {
            return String(_lastTemperature, 1) + "°C, " + String(_lastHumidity, 1) + "%";
        }
        return "Sensor Error";
    }
    
    // Reset the sensor
    bool reset()
    {
        if (!_initialized) return false;
        
        bool success = sendCommand(SHT30_CMD_SOFT_RESET);
        if (success)
        {
            delay(50);
            Serial.print(DEVICE_TYPE);
            Serial.println(" SHT30 sensor reset");
        }
        return success;
    }
};

#endif // COMPILE_TEMPERATURE_SENSOR

#endif // SHT30_TEMPERATURE_SENSOR_H
