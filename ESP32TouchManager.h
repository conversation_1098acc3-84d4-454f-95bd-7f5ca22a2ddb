#ifndef ESP32_TOUCH_MANAGER_H
#define ESP32_TOUCH_MANAGER_H

#include "DeviceConfig.h"

// Only compile this manager for ESP32 devices
#ifdef COMPILE_ESP32_TOUCH

class ESP32TouchManager
{
private:
    const uint8_t _touchPins[MAX_SWITCHES] = TOUCH_PINS;
    uint16_t _touchThresholds[MAX_SWITCHES];
    uint16_t _baselineValues[MAX_SWITCHES];
    bool _lastTouchState[MAX_SWITCHES];
    unsigned long _lastTouchTime[MAX_SWITCHES];
    unsigned long _debounceDelay;
    uint8_t _switchCount;
    
    // Advanced touch features
    bool _autoCalibration;
    unsigned long _lastCalibrationTime;
    static const unsigned long CALIBRATION_INTERVAL = 60000; // 1 minute
    static const uint8_t CALIBRATION_SAMPLES = 10;
    
    // Touch sensitivity settings
    uint8_t _sensitivity;
    static const uint8_t MIN_SENSITIVITY = 1;
    static const uint8_t MAX_SENSITIVITY = 10;
    static const uint8_t DEFAULT_SENSITIVITY = 5;
    
    // Reset functionality
    bool _resetSensorsPressed;
    unsigned long _resetSensorsPressStartTime;
    bool _resetTriggered;
    static const unsigned long RESET_HOLD_TIME = 5000; // 5 seconds
    
    // Callback functions
    void (*_touchCallback)(uint8_t switchIndex, bool pressed);
    void (*_resetCallback)();
    
    // Calculate dynamic threshold based on baseline and sensitivity
    uint16_t calculateThreshold(uint8_t switchIndex)
    {
        uint16_t baseline = _baselineValues[switchIndex];
        uint8_t reduction = map(_sensitivity, MIN_SENSITIVITY, MAX_SENSITIVITY, 5, 50);
        return baseline - (baseline * reduction / 100);
    }
    
    // Calibrate touch sensors
    void calibrateSensors()
    {
        Serial.print(DEVICE_TYPE);
        Serial.println(" Calibrating ESP32 touch sensors...");
        
        for (int i = 0; i < _switchCount; i++)
        {
            uint32_t total = 0;
            
            // Take multiple samples for stable baseline
            for (int sample = 0; sample < CALIBRATION_SAMPLES; sample++)
            {
                total += touchRead(_touchPins[i]);
                delay(10);
            }
            
            _baselineValues[i] = total / CALIBRATION_SAMPLES;
            _touchThresholds[i] = calculateThreshold(i);
            
            Serial.print("Touch sensor ");
            Serial.print(i + 1);
            Serial.print(" (T");
            Serial.print(_touchPins[i]);
            Serial.print("): baseline=");
            Serial.print(_baselineValues[i]);
            Serial.print(", threshold=");
            Serial.println(_touchThresholds[i]);
        }
        
        _lastCalibrationTime = millis();
        Serial.print(DEVICE_TYPE);
        Serial.println(" Touch sensor calibration complete");
    }

public:
    ESP32TouchManager(uint8_t switchCount = SWITCH_COUNT)
        : _debounceDelay(50), _switchCount(switchCount), _autoCalibration(true),
          _lastCalibrationTime(0), _sensitivity(DEFAULT_SENSITIVITY),
          _resetSensorsPressed(false), _resetSensorsPressStartTime(0), _resetTriggered(false),
          _touchCallback(nullptr), _resetCallback(nullptr)
    {
        // Initialize arrays
        for (int i = 0; i < MAX_SWITCHES; i++)
        {
            _touchThresholds[i] = TOUCH_THRESHOLD;
            _baselineValues[i] = 1000; // Default baseline
            _lastTouchState[i] = false;
            _lastTouchTime[i] = 0;
        }
        
        Serial.print(DEVICE_TYPE);
        Serial.println(" ESP32TouchManager constructor");
        Serial.print("Touch pins: ");
        for (int i = 0; i < _switchCount; i++)
        {
            Serial.print("T");
            Serial.print(_touchPins[i]);
            if (i < _switchCount - 1) Serial.print(", ");
        }
        Serial.println();
    }
    
    // Initialize touch sensors
    void begin()
    {
        Serial.print(DEVICE_TYPE);
        Serial.println(" Initializing ESP32 capacitive touch sensors...");
        
        // ESP32 touch sensors don't need pinMode setup
        // They are automatically configured when touchRead() is called
        
        // Perform initial calibration
        calibrateSensors();
        
        Serial.print(DEVICE_TYPE);
        Serial.println(" ESP32 Touch Sensor Manager initialized");
        Serial.print("Sensitivity: ");
        Serial.print(_sensitivity);
        Serial.print("/");
        Serial.println(MAX_SENSITIVITY);
    }
    
    // Read touch sensor with advanced filtering
    bool readTouchSensor(uint8_t switchIndex)
    {
        if (switchIndex >= _switchCount)
            return false;
            
        uint16_t touchValue = touchRead(_touchPins[switchIndex]);
        bool touched = touchValue < _touchThresholds[switchIndex];
        
        // Optional: Update baseline for drift compensation
        if (_autoCalibration && !touched)
        {
            // Slowly adjust baseline if sensor is not touched
            _baselineValues[switchIndex] = (_baselineValues[switchIndex] * 99 + touchValue) / 100;
        }
        
        return touched;
    }
    
    // Get raw touch value for debugging
    uint16_t getRawTouchValue(uint8_t switchIndex)
    {
        if (switchIndex >= _switchCount)
            return 0;
            
        return touchRead(_touchPins[switchIndex]);
    }
    
    // Set touch sensitivity (1-10, higher = more sensitive)
    void setSensitivity(uint8_t sensitivity)
    {
        if (sensitivity < MIN_SENSITIVITY) sensitivity = MIN_SENSITIVITY;
        if (sensitivity > MAX_SENSITIVITY) sensitivity = MAX_SENSITIVITY;
        
        _sensitivity = sensitivity;
        
        // Recalculate thresholds
        for (int i = 0; i < _switchCount; i++)
        {
            _touchThresholds[i] = calculateThreshold(i);
        }
        
        Serial.print(DEVICE_TYPE);
        Serial.print(" Touch sensitivity set to ");
        Serial.print(_sensitivity);
        Serial.print("/");
        Serial.println(MAX_SENSITIVITY);
    }
    
    // Get current sensitivity
    uint8_t getSensitivity()
    {
        return _sensitivity;
    }
    
    // Enable/disable auto-calibration
    void setAutoCalibration(bool enabled)
    {
        _autoCalibration = enabled;
        Serial.print(DEVICE_TYPE);
        Serial.print(" Touch auto-calibration ");
        Serial.println(enabled ? "enabled" : "disabled");
    }
    
    // Manual recalibration
    void recalibrate()
    {
        calibrateSensors();
    }
    
    // Set callback functions
    void setTouchCallback(void (*callback)(uint8_t switchIndex, bool pressed))
    {
        _touchCallback = callback;
    }
    
    void setResetCallback(void (*callback)())
    {
        _resetCallback = callback;
    }
    
    // Main update method - call this in loop()
    void handleTouchSensors()
    {
        unsigned long currentTime = millis();
        
        // Auto-calibration check
        if (_autoCalibration && currentTime - _lastCalibrationTime > CALIBRATION_INTERVAL)
        {
            calibrateSensors();
        }
        
        // Check each touch sensor
        for (int i = 0; i < _switchCount; i++)
        {
            bool currentState = readTouchSensor(i);
            
            // Debouncing
            if (currentState != _lastTouchState[i])
            {
                if (currentTime - _lastTouchTime[i] > _debounceDelay)
                {
                    _lastTouchState[i] = currentState;
                    _lastTouchTime[i] = currentTime;
                    
                    if (currentState) // Touch pressed
                    {
                        Serial.print(DEVICE_TYPE);
                        Serial.print(" Capacitive touch sensor ");
                        Serial.print(i + 1);
                        Serial.print(" pressed (value: ");
                        Serial.print(getRawTouchValue(i));
                        Serial.print(", threshold: ");
                        Serial.print(_touchThresholds[i]);
                        Serial.println(")");
                        
                        if (_touchCallback)
                        {
                            _touchCallback(i, true);
                        }
                    }
                    else // Touch released
                    {
                        Serial.print(DEVICE_TYPE);
                        Serial.print(" Capacitive touch sensor ");
                        Serial.print(i + 1);
                        Serial.println(" released");
                        
                        if (_touchCallback)
                        {
                            _touchCallback(i, false);
                        }
                    }
                }
            }
        }
        
        // Handle reset functionality
        handleResetSequence();
    }
    
    // Handle reset sequence
    void handleResetSequence()
    {
        // Check if configured reset sensors are currently pressed
        bool resetSensorsPressed = false;
        if (_switchCount > RESET_TOUCH_1 && _switchCount > RESET_TOUCH_2)
        {
            resetSensorsPressed = readTouchSensor(RESET_TOUCH_1) && readTouchSensor(RESET_TOUCH_2);
        }
        
        if (resetSensorsPressed && !_resetSensorsPressed)
        {
            // Reset sequence started
            _resetSensorsPressed = true;
            _resetSensorsPressStartTime = millis();
            _resetTriggered = false;
            
            Serial.print("=== CAPACITIVE TOUCH SENSORS ");
            Serial.print(RESET_TOUCH_1 + 1);
            Serial.print(" & ");
            Serial.print(RESET_TOUCH_2 + 1);
            Serial.println(" PRESSED ===");
            Serial.println("Hold for 5 seconds to reset device...");
            Serial.print("Watch sensor ");
            Serial.print(RESET_FEEDBACK_SWITCH + 1);
            Serial.println(" RGB light flash red!");
        }
        else if (!resetSensorsPressed && _resetSensorsPressed)
        {
            // Reset sequence cancelled
            _resetSensorsPressed = false;
            if (!_resetTriggered)
            {
                Serial.print("Reset sequence cancelled - sensors ");
                Serial.print(RESET_TOUCH_1 + 1);
                Serial.print(" & ");
                Serial.print(RESET_TOUCH_2 + 1);
                Serial.println(" not both pressed");
            }
        }
        else if (_resetSensorsPressed && !_resetTriggered)
        {
            // Check if hold time reached
            if (millis() - _resetSensorsPressStartTime >= RESET_HOLD_TIME)
            {
                _resetTriggered = true;
                
                Serial.println("=== DEVICE RESET TRIGGERED ===");
                Serial.print("Capacitive touch sensors ");
                Serial.print(RESET_TOUCH_1 + 1);
                Serial.print(" & ");
                Serial.print(RESET_TOUCH_2 + 1);
                Serial.println(" held for 5 seconds");
                Serial.print("Restarting ");
                Serial.print(DEVICE_TYPE);
                Serial.println(" in 2 seconds...");
                
                if (_resetCallback)
                {
                    _resetCallback();
                }
                
                delay(2000);
                ESP_RESTART();
            }
        }
    }
    
    // Get touch sensor status
    String getTouchStatus(uint8_t switchIndex)
    {
        if (switchIndex >= _switchCount)
            return "Invalid sensor";
            
        String status = "T" + String(_touchPins[switchIndex]);
        status += ": raw=" + String(getRawTouchValue(switchIndex));
        status += ", threshold=" + String(_touchThresholds[switchIndex]);
        status += ", baseline=" + String(_baselineValues[switchIndex]);
        status += ", state=" + String(_lastTouchState[switchIndex] ? "PRESSED" : "RELEASED");
        
        return status;
    }
    
    // Get overall status
    String getStatus()
    {
        String status = "Sensitivity: " + String(_sensitivity) + "/" + String(MAX_SENSITIVITY);
        status += ", Auto-cal: " + String(_autoCalibration ? "ON" : "OFF");
        status += ", Sensors: " + String(_switchCount);
        
        return status;
    }
    
    // Check if reset is in progress
    bool isResetInProgress()
    {
        return _resetSensorsPressed;
    }
};

#endif // COMPILE_ESP32_TOUCH

#endif // ESP32_TOUCH_MANAGER_H
