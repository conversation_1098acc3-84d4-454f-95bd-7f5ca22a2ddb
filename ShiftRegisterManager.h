#ifndef SHIFT_REGISTER_MANAGER_H
#define SHIFT_REGISTER_MANAGER_H

#include <Arduino.h>
#include "DeviceConfig.h"

class ShiftRegisterManager
{
private:
    uint8_t _dataPin;
    uint8_t _clockPin;
    uint8_t _latchPin;
    uint16_t _outputState;

    // Hardware-specific bit mapping for dual shift register setup
    // Data structure: x1x2x3x4x5x6x7x8 x9x10x11x12x13x14x15x16
    // Transmission order: x9x10x11x12x13x14x15x16x1x2x3x4x5x6x7x8
    // Bit mappings are configured per device model in DeviceConfig.h

    // Bit position arrays for hardware mapping (configuration-based)
#if HAS_RELAYS
    const uint8_t RELAY_BITS[MAX_SWITCHES] = RELAY_BIT_POSITIONS;
#endif
    const uint8_t RED_BITS[MAX_SWITCHES] = RED_BIT_POSITIONS;
    const uint8_t GREEN_BITS[MAX_SWITCHES] = GREEN_BIT_POSITIONS;
    const uint8_t BLUE_BITS[MAX_SWITCHES] = BLUE_BIT_POSITIONS;

    // Update the physical shift registers with current state
    void updateShiftRegisters()
    {
        digitalWrite(_latchPin, LOW);

        // Send bits 9-16 first (goes to second 74HC595)
        shiftOut(_dataPin, _clockPin, MSBFIRST, (_outputState >> 8) & 0xFF);

        // Send bits 1-8 second (stays in first 74HC595)
        shiftOut(_dataPin, _clockPin, MSBFIRST, _outputState & 0xFF);

        digitalWrite(_latchPin, HIGH);
    }

public:
    ShiftRegisterManager(uint8_t dataPin, uint8_t clockPin, uint8_t latchPin)
        : _dataPin(dataPin), _clockPin(clockPin), _latchPin(latchPin), _outputState(0)
    {
    }

    // Initialize the shift register pins
    void begin()
    {
        pinMode(_dataPin, OUTPUT);
        pinMode(_clockPin, OUTPUT);
        pinMode(_latchPin, OUTPUT);

        // Initialize all outputs to LOW
        _outputState = 0;
        updateShiftRegisters();

        Serial.print(DEVICE_TYPE);
        Serial.println(" Shift Register Manager initialized");
        Serial.print("Data Pin: ");
        Serial.println(_dataPin);
        Serial.print("Clock Pin: ");
        Serial.println(_clockPin);
        Serial.print("Latch Pin: ");
        Serial.println(_latchPin);
    }

    // Set relay state (only for devices with relays)
    void setRelay(uint8_t relayIndex, bool state)
    {
#if HAS_RELAYS
        if (relayIndex >= SWITCH_COUNT)
            return;

        uint8_t bitPos = RELAY_BITS[relayIndex];
#else
        // No relays on this device model
        return;
#endif

#if HAS_RELAYS
        if (state)
        {
            _outputState |= (1 << bitPos);
        }
        else
        {
            _outputState &= ~(1 << bitPos);
        }

        updateShiftRegisters();

        Serial.print(DEVICE_TYPE);
        Serial.print(" Relay ");
        Serial.print(relayIndex + 1);
        Serial.print(" set to ");
        Serial.println(state ? "ON" : "OFF");
#endif
    }

    // Get relay state (only for devices with relays)
    bool getRelay(uint8_t relayIndex)
    {
#if HAS_RELAYS
        if (relayIndex >= SWITCH_COUNT)
            return false;

        uint8_t bitPos = RELAY_BITS[relayIndex];
        return (_outputState & (1 << bitPos)) != 0;
#else
        return false; // No relays on this device model
#endif
    }

    // Set RGB color for a specific switch button
    // Note: RGB values are digital (0=OFF, >0=ON) since 74HC595 provides digital outputs only
    void setRGB(uint8_t switchIndex, uint8_t r, uint8_t g, uint8_t b)
    {
        if (switchIndex >= SWITCH_COUNT)
            return;

        // Set Red (digital: 0=OFF, >0=ON)
        if (r > 0)
        {
            _outputState |= (1 << RED_BITS[switchIndex]);
        }
        else
        {
            _outputState &= ~(1 << RED_BITS[switchIndex]);
        }

        // Set Green (digital: 0=OFF, >0=ON)
        if (g > 0)
        {
            _outputState |= (1 << GREEN_BITS[switchIndex]);
        }
        else
        {
            _outputState &= ~(1 << GREEN_BITS[switchIndex]);
        }

        // Set Blue (digital: 0=OFF, >0=ON)
        if (b > 0)
        {
            _outputState |= (1 << BLUE_BITS[switchIndex]);
        }
        else
        {
            _outputState &= ~(1 << BLUE_BITS[switchIndex]);
        }

        updateShiftRegisters();

        Serial.print(DEVICE_TYPE);
        Serial.print(" RGB for Switch ");
        Serial.print(switchIndex + 1);
        Serial.print(" set to R:");
        Serial.print(r > 0 ? "ON" : "OFF");
        Serial.print(" G:");
        Serial.print(g > 0 ? "ON" : "OFF");
        Serial.print(" B:");
        Serial.println(b > 0 ? "ON" : "OFF");
    }

    // Get RGB color for a specific switch button
    void getRGB(uint8_t switchIndex, uint8_t &r, uint8_t &g, uint8_t &b)
    {
        if (switchIndex >= SWITCH_COUNT)
        {
            r = g = b = 0;
            return;
        }

        r = (_outputState & (1 << RED_BITS[switchIndex])) ? 255 : 0;
        g = (_outputState & (1 << GREEN_BITS[switchIndex])) ? 255 : 0;
        b = (_outputState & (1 << BLUE_BITS[switchIndex])) ? 255 : 0;
    }

    // Set individual output bit (for advanced control)
    void setOutput(uint8_t bitIndex, bool state)
    {
        if (bitIndex >= 16)
            return;

        if (state)
        {
            _outputState |= (1 << bitIndex);
        }
        else
        {
            _outputState &= ~(1 << bitIndex);
        }

        updateShiftRegisters();
    }

    // Get individual output bit state
    bool getOutput(uint8_t bitIndex)
    {
        if (bitIndex >= 16)
            return false;
        return (_outputState & (1 << bitIndex)) != 0;
    }

    // Set all outputs at once
    void setAllOutputs(uint16_t state)
    {
        _outputState = state;
        updateShiftRegisters();
    }

    // Get current output state
    uint16_t getAllOutputs()
    {
        return _outputState;
    }

    // Clear all outputs
    void clearAll()
    {
        _outputState = 0;
        updateShiftRegisters();
        Serial.print("All ");
        Serial.print(DEVICE_TYPE);
        Serial.println(" shift register outputs cleared");
    }
};

#endif // SHIFT_REGISTER_MANAGER_H
