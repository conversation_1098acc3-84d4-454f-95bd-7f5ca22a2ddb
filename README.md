# Multi-Device Switch Prototype

A comprehensive IoT switch control system supporting multiple device models with advanced RGB lighting, environmental monitoring, and web-based control interfaces.

## 🚀 Overview

This project provides a unified codebase that supports 6 different device models ranging from simple 1-switch controllers to advanced ESP32-based cooler control systems with full-color RGB, temperature monitoring, and RF remote control.

## 📋 Supported Device Models

### ESP8266 Devices (Shift Register Control)
- **1-Switch Model**: Single relay + basic RGB control
- **2-Switch Model**: Dual relay + basic RGB control  
- **3-Switch Model**: Triple relay + basic RGB control (default)
- **4-Switch Model**: Quad relay + basic RGB control
- **Scenario Key Model**: 4 RGB keys without relays

### ESP32 Devices (Direct Pin Control)
- **Cooler Control Model**: Advanced system with full-color RGB, temperature sensor, RF receiver, and buzzer

## ⚡ Quick Start

### 1. Device Selection
Edit `DeviceConfig.h` and uncomment your target device:

```cpp
// Uncomment ONE device model
// #define DEVICE_MODEL_1_SWITCH
// #define DEVICE_MODEL_2_SWITCH
#define DEVICE_MODEL_3_SWITCH        // Currently active
// #define DEVICE_MODEL_4_SWITCH
// #define DEVICE_MODEL_COOLER_CONTROL
// #define DEVICE_MODEL_SCENARIO_KEY
```

### 2. Hardware Setup
Connect your hardware according to the pin definitions in `DeviceConfig.h` for your selected model.

### 3. Compile and Upload
- Select the appropriate board (ESP8266 NodeMCU or ESP32 Dev Module)
- Compile and upload the code
- The system will automatically configure features based on your device selection

### 4. Initial Setup
1. Device creates WiFi AP: `ESP_Switch_Setup`
2. Connect and navigate to `192.168.4.1`
3. Configure WiFi, MQTT, and device settings
4. Device will restart and connect to your network

## 🔧 Features by Device Model

| Feature | 1SW | 2SW | 3SW | 4SW | Cooler | Scenario |
|---------|-----|-----|-----|-----|--------|----------|
| **Hardware Control** |
| Shift Registers | ✅ | ✅ | ✅ | ✅ | ❌ | ✅ |
| Direct GPIO | ❌ | ❌ | ❌ | ❌ | ✅ | ❌ |
| **Lighting** |
| Basic RGB (8 colors) | ✅ | ✅ | ✅ | ✅ | ❌ | ✅ |
| Full-Color RGB (16.7M) | ❌ | ❌ | ❌ | ❌ | ✅ | ❌ |
| Color Cycling | ❌ | ❌ | ❌ | ❌ | ✅ | ❌ |
| **Input** |
| Digital Touch | ✅ | ✅ | ✅ | ✅ | ❌ | ✅ |
| Capacitive Touch | ❌ | ❌ | ❌ | ❌ | ✅ | ❌ |
| **Advanced Features** |
| Temperature Sensor | ❌ | ❌ | ❌ | ❌ | ✅ | ❌ |
| RF Remote Control | ❌ | ❌ | ❌ | ❌ | ✅ | ❌ |
| Audio Feedback | ❌ | ❌ | ❌ | ❌ | ✅ | ❌ |
| **Connectivity** |
| WiFi | ✅ | ✅ | ✅ | ✅ | ✅ | ✅ |
| MQTT | ✅ | ✅ | ✅ | ✅ | ✅ | ✅ |
| OTA Updates | ✅ | ✅ | ✅ | ✅ | ✅ | ✅ |
| Web Interface | ✅ | ✅ | ✅ | ✅ | ✅ | ✅ |

## 🌐 Web Interface

### ESP8266 Devices
- **Simple Interface**: Basic RGB color selection (8 colors)
- **Real-Time Updates**: Live switch state monitoring
- **Mobile Responsive**: Optimized for all screen sizes
- **Memory Efficient**: ~8KB template size

### ESP32 Devices  
- **Advanced Interface**: Full-color RGB with color picker
- **Color Cycling**: 8 dynamic lighting modes
- **Global Controls**: Brightness, speed, saturation
- **Preset Sequences**: 6 built-in lighting scenes
- **Professional Design**: ~15KB template with advanced features

### Available Pages
- **Dashboard**: Device status and system information
- **WiFi Settings**: Network configuration and scanning
- **Switch Control**: Device-specific control interface
- **AP Control**: Access Point management
- **MQTT Settings**: Message broker configuration
- **System Updates**: OTA firmware updates

## 🎨 RGB Control Features

### Basic RGB (ESP8266)
- 8 predefined colors: Red, Green, Blue, Yellow, Cyan, Magenta, White, Off
- Separate ON/OFF color settings per switch
- Real-time color changes via web interface

### Advanced RGB (ESP32)
- **Full Color Range**: 0-255 RGB values (16.7M colors)
- **Color Input Methods**:
  - Visual color picker
  - Direct RGB value input
  - Preset color buttons
- **Hardware PWM**: Smooth color transitions
- **Global Brightness**: Master brightness control

### Color Cycling Modes (ESP32)
1. **Rainbow**: Smooth spectrum cycling
2. **Breathing**: Fade in/out effects
3. **Strobe**: Fast flashing patterns  
4. **Fade**: Smooth color transitions
5. **Wave**: Sine wave intensity modulation
6. **Fire**: Flickering fire simulation
7. **Ocean**: Blue wave patterns
8. **Custom**: User-defined sequences

### Preset Lighting Scenes
- **Sunset**: Warm red/orange/yellow transitions
- **Ocean Waves**: Cool blue/cyan patterns
- **Forest**: Natural green variations
- **Party**: Fast multi-color cycling
- **Relax**: Slow purple/blue transitions
- **Energize**: Dynamic orange/yellow patterns

## 📡 MQTT Integration

### Device Topics
```
devices/{deviceID}/status          # Device status updates
devices/{deviceID}/switch/{index}  # Individual switch control
devices/{deviceID}/rgb/on/{index}  # RGB ON color setting
devices/{deviceID}/rgb/off/{index} # RGB OFF color setting
devices/{deviceID}/name            # Device name updates
```

### ESP32 Advanced Topics
```
devices/{deviceID}/rgb/set         # Full RGB color control
devices/{deviceID}/rgb/brightness  # Global brightness
devices/{deviceID}/cycle/mode      # Color cycle mode
devices/{deviceID}/cycle/speed     # Cycle speed
devices/{deviceID}/temperature     # Temperature readings (Cooler model)
devices/{deviceID}/rf/command      # RF remote commands (Cooler model)
```

### Message Formats
```json
// Switch State
{
  "deviceID": "ESP_12345678",
  "switchIndex": 0,
  "state": true,
  "timestamp": 1640995200
}

// RGB Color (ESP32)
{
  "deviceID": "ESP_12345678", 
  "switchIndex": 0,
  "r": 255,
  "g": 128,
  "b": 64
}

// Temperature Reading (Cooler model)
{
  "deviceID": "ESP_12345678",
  "temperature": 23.5,
  "humidity": 65.2,
  "timestamp": 1640995200
}
```

## 🔧 Hardware Configuration

### ESP8266 Models (Shift Register)
```cpp
// 3-Switch Model Example
#define SWITCH_COUNT 3
#define DATA_PIN 13
#define LATCH_PIN 14  
#define CLOCK_PIN 16
#define TOUCH_PINS {12, 0, 2}
#define RELAY_BITS {11, 10, 9}
#define RED_BITS {1, 4, 7}
#define GREEN_BITS {3, 6, 8}
#define BLUE_BITS {2, 5, 15}
```

### ESP32 Model (Direct Pin)
```cpp
// Cooler Control Model
#define SWITCH_COUNT 3
#define RELAY_PIN_NUMBERS {2, 4, 16}
#define RED_PIN_NUMBERS {17, 5, 18}
#define GREEN_PIN_NUMBERS {19, 21, 3}
#define BLUE_PIN_NUMBERS {22, 23, 1}
#define TOUCH_PINS {T0, T3, T4}
#define TEMP_SENSOR_SDA_PIN 25
#define TEMP_SENSOR_SCL_PIN 26
#define RF_RECEIVER_PIN 27
#define BUZZER_PIN 32
```

## 🔄 OTA Updates

### Automatic Updates
- Checks for updates every 12 hours
- Configurable auto-update toggle
- Visual feedback during update process
- Automatic restart after successful update

### Manual Updates
- Web interface update button
- Real-time progress tracking (ESP32)
- Server reachability status
- Release notes display

### Update Server Setup
Configure your update server in `DeviceConfig.h`:
```cpp
#define OTA_SERVER_HOST "*************"
#define OTA_SERVER_PORT 8080
#define UPDATE_CHECK_ENDPOINT "/api/update/check"
#define FIRMWARE_DOWNLOAD_ENDPOINT "/api/update/download"
```

## 🌡️ Environmental Monitoring (Cooler Model)

### SHT30 Temperature Sensor
- **Temperature Range**: -40°C to +125°C
- **Humidity Range**: 0% to 100% RH
- **Accuracy**: ±0.3°C, ±2% RH
- **Interface**: I2C with CRC validation
- **Auto-Calibration**: Drift compensation
- **Configurable Alerts**: Temperature threshold monitoring

### Features
- Real-time temperature and humidity monitoring
- Web interface display with live updates
- MQTT publishing for external monitoring
- Configurable alert thresholds
- Historical data logging capability

## 📻 RF Remote Control (Cooler Model)

### RXB22 RF Receiver
- **Frequency**: 433MHz
- **Protocol**: Custom protocol decoding
- **Range**: Up to 100 meters (line of sight)
- **Commands**: Power, temperature control, emergency stop
- **Audio Feedback**: Buzzer confirmation for received commands

### Supported Commands
- Power toggle
- Temperature threshold adjustment
- Emergency stop
- Scene selection
- Custom command mapping

## 🔊 Audio Feedback (Cooler Model)

### Buzzer Manager
- **Startup Sound**: Rising tone sequence
- **Success Sound**: High frequency confirmation
- **Error Sound**: Low frequency warning
- **Alert Sound**: Alternating high-low pattern
- **RF Command Feedback**: Different tones per command type
- **Temperature Alerts**: Rapid beeping for threshold exceeded

## 🔒 Security Features

### WiFi Security
- WPA2/WPA3 support
- Hidden SSID capability
- MAC address filtering support
- Automatic reconnection with exponential backoff

### Web Interface Security
- Request throttling (rate limiting)
- Input validation and sanitization
- CSRF protection
- Secure cookie handling

### MQTT Security
- Username/password authentication
- TLS/SSL encryption support
- Topic-based access control
- Message validation

## 📊 Memory Optimization

### ESP8266 Optimizations
- **Program Memory**: 25-28KB (optimized for each model)
- **RAM Usage**: ~2KB (efficient buffer management)
- **Template Loading**: Conditional compilation
- **Feature Exclusion**: Only necessary code included

### ESP32 Capabilities
- **Program Memory**: ~35KB (full feature set)
- **RAM Usage**: ~4KB (advanced features enabled)
- **PWM Channels**: 12 channels for RGB control
- **Advanced Features**: Full sensor integration

## 🛠️ Development Setup

### Prerequisites
- Arduino IDE 1.8.19+ or PlatformIO
- ESP8266 Board Package 3.0.0+
- ESP32 Board Package 2.0.0+

### Required Libraries
```cpp
// ESP8266
#include <ESP8266WiFi.h>
#include <ESP8266WebServer.h>
#include <ESP8266HTTPClient.h>
#include <ESP8266httpUpdate.h>

// ESP32  
#include <WiFi.h>
#include <WebServer.h>
#include <HTTPClient.h>
#include <HTTPUpdate.h>

// Common
#include <ArduinoJson.h>
#include <PubSubClient.h>
#include <EEPROM.h>
```

### Compilation
1. Select your target board
2. Configure device model in `DeviceConfig.h`
3. Verify pin assignments match your hardware
4. Compile and upload

## 📁 Project Structure

```
Switch_Prototype/
├── Switch_Prototype.ino          # Main application file
├── DeviceConfig.h                # Device configuration and pin definitions
├── DeviceManager.h               # Core device management
├── WiFiManager.h                 # WiFi connection management
├── WebServerManager.h            # Web interface and API
├── MQTTManager.h                 # MQTT communication
├── OTAManager.h                  # Over-the-air updates
├── TouchSensorManager.h          # Touch input handling
├── ShiftRegisterManager.h        # 74HC595 shift register control
├── DirectPinManager.h            # ESP32 direct GPIO control
├── FullColorRGBManager.h         # Advanced RGB control (ESP32)
├── ColorCycleManager.h           # Color cycling effects (ESP32)
├── ESP32TouchManager.h           # Capacitive touch (ESP32)
├── SHT30TemperatureSensor.h      # Temperature sensor (Cooler)
├── RXB22RFReceiver.h             # RF receiver (Cooler)
├── BuzzerManager.h               # Audio feedback (Cooler)
├── CoolerControlManager.h        # Integrated cooler management
├── WebTemplates_ESP8266.h        # Basic web interface templates
├── WebTemplates_ESP32.h          # Advanced web interface templates
└── Documentation/                # Project documentation
    ├── README.md                 # This file
    ├── FinalProjectSummary.md    # Complete project overview
    ├── WebServerUpdateSummary.md # Web interface details
    ├── ConfigurationTest.md      # Validation procedures
    └── ConditionalCompilation.md # Build system documentation
```

## 🔧 Troubleshooting

### Common Issues

**WiFi Connection Problems**
- Check SSID and password in web interface
- Verify 2.4GHz network (ESP8266 doesn't support 5GHz)
- Reset device and reconfigure if needed

**Web Interface Not Loading**
- Ensure device is connected to network
- Check IP address in serial monitor
- Verify sufficient free memory (>30KB for ESP8266)

**MQTT Not Connecting**
- Verify broker address and port
- Check username/password if required
- Ensure network connectivity

**RGB Colors Not Working**
- Verify pin connections match configuration
- Check power supply capacity for RGB LEDs
- Test individual colors to isolate issues

**Touch Sensors Not Responding**
- Clean sensor surfaces
- Adjust sensitivity settings (ESP32)
- Check pin connections and grounding

### Debug Information
Enable detailed logging by setting:
```cpp
#define DEBUG_LEVEL 2  // 0=None, 1=Basic, 2=Detailed
```

### Factory Reset
Hold touch sensors 1 and 3 simultaneously for 5 seconds to perform factory reset.

## 🤝 Contributing

1. Fork the repository
2. Create a feature branch
3. Make your changes
4. Test on target hardware
5. Submit a pull request

### Adding New Device Models
1. Add device definition to `DeviceConfig.h`
2. Define pin mappings and features
3. Update conditional compilation flags
4. Create device-specific web templates if needed
5. Test all functionality

## 📄 License

This project is licensed under the MIT License - see the LICENSE file for details.

## 🙏 Acknowledgments

- ESP8266/ESP32 Arduino Core teams
- ArduinoJson library by Benoit Blanchon
- PubSubClient library by Nick O'Leary
- Community contributors and testers

## 📞 Support

For support and questions:
- Create an issue on GitHub
- Check the documentation in the `/Documentation` folder
- Review the troubleshooting section above

---

**Multi-Device Switch Prototype** - A comprehensive IoT control system supporting multiple hardware configurations with advanced web interfaces and environmental monitoring capabilities.
