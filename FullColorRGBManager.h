#ifndef FULL_COLOR_RGB_MANAGER_H
#define FULL_COLOR_RGB_MANAGER_H

#include "DeviceConfig.h"

// Only compile this manager for ESP32 devices with full-color RGB
#ifdef COMPILE_FULL_COLOR_RGB

class FullColorRGBManager
{
private:
    // RGB color structure
    struct RGBColor
    {
        uint8_t r, g, b;
        RGBColor(uint8_t red = 0, uint8_t green = 0, uint8_t blue = 0) : r(red), g(green), b(blue) {}
    };
    
    // HSV color structure for smooth transitions
    struct HSVColor
    {
        uint16_t h; // 0-360
        uint8_t s;  // 0-100
        uint8_t v;  // 0-100
        HSVColor(uint16_t hue = 0, uint8_t saturation = 100, uint8_t value = 100) : h(hue), s(saturation), v(value) {}
    };
    
    // Transition structure
    struct ColorTransition
    {
        RGBColor startColor;
        RGBColor targetColor;
        RGBColor currentColor;
        unsigned long startTime;
        unsigned long duration;
        bool active;
        uint8_t switchIndex;
    };
    
    uint8_t _switchCount;
    RGBColor _currentColors[MAX_SWITCHES];
    RGBColor _targetColors[MAX_SWITCHES];
    ColorTransition _transitions[MAX_SWITCHES];
    
    // Brightness control
    uint8_t _globalBrightness;
    uint8_t _switchBrightness[MAX_SWITCHES];
    
    // Smooth transition settings
    bool _smoothTransitions;
    unsigned long _defaultTransitionTime;
    
    // Color presets
    static const RGBColor COLOR_OFF;
    static const RGBColor COLOR_RED;
    static const RGBColor COLOR_GREEN;
    static const RGBColor COLOR_BLUE;
    static const RGBColor COLOR_WHITE;
    static const RGBColor COLOR_YELLOW;
    static const RGBColor COLOR_CYAN;
    static const RGBColor COLOR_MAGENTA;
    static const RGBColor COLOR_ORANGE;
    static const RGBColor COLOR_PURPLE;
    static const RGBColor COLOR_WARM_WHITE;
    static const RGBColor COLOR_COOL_WHITE;
    
    // Convert HSV to RGB
    RGBColor hsvToRgb(const HSVColor& hsv)
    {
        float h = hsv.h / 60.0;
        float s = hsv.s / 100.0;
        float v = hsv.v / 100.0;
        
        int i = (int)h;
        float f = h - i;
        float p = v * (1 - s);
        float q = v * (1 - s * f);
        float t = v * (1 - s * (1 - f));
        
        float r, g, b;
        
        switch (i % 6)
        {
            case 0: r = v; g = t; b = p; break;
            case 1: r = q; g = v; b = p; break;
            case 2: r = p; g = v; b = t; break;
            case 3: r = p; g = q; b = v; break;
            case 4: r = t; g = p; b = v; break;
            case 5: r = v; g = p; b = q; break;
            default: r = g = b = 0; break;
        }
        
        return RGBColor((uint8_t)(r * 255), (uint8_t)(g * 255), (uint8_t)(b * 255));
    }
    
    // Apply brightness to color
    RGBColor applyBrightness(const RGBColor& color, uint8_t brightness)
    {
        if (brightness == 255) return color;
        
        return RGBColor(
            (color.r * brightness) / 255,
            (color.g * brightness) / 255,
            (color.b * brightness) / 255
        );
    }
    
    // Linear interpolation between colors
    RGBColor interpolateColor(const RGBColor& start, const RGBColor& end, float progress)
    {
        if (progress <= 0.0) return start;
        if (progress >= 1.0) return end;
        
        return RGBColor(
            start.r + (uint8_t)((end.r - start.r) * progress),
            start.g + (uint8_t)((end.g - start.g) * progress),
            start.b + (uint8_t)((end.b - start.b) * progress)
        );
    }
    
    // Update hardware PWM
    void updateHardwareRGB(uint8_t switchIndex, const RGBColor& color)
    {
        if (switchIndex >= _switchCount) return;
        
        // Apply global and switch-specific brightness
        uint8_t combinedBrightness = (_globalBrightness * _switchBrightness[switchIndex]) / 255;
        RGBColor finalColor = applyBrightness(color, combinedBrightness);
        
        // Use DirectPinManager for hardware control
        // This assumes DirectPinManager is available and handles PWM
        // In a real implementation, you'd call the hardware PWM functions here
        Serial.print("RGB");
        Serial.print(switchIndex + 1);
        Serial.print(": R=");
        Serial.print(finalColor.r);
        Serial.print(", G=");
        Serial.print(finalColor.g);
        Serial.print(", B=");
        Serial.println(finalColor.b);
    }

public:
    FullColorRGBManager(uint8_t switchCount = SWITCH_COUNT)
        : _switchCount(switchCount), _globalBrightness(255), _smoothTransitions(true), _defaultTransitionTime(500)
    {
        // Initialize colors and brightness
        for (int i = 0; i < MAX_SWITCHES; i++)
        {
            _currentColors[i] = COLOR_OFF;
            _targetColors[i] = COLOR_OFF;
            _switchBrightness[i] = 255;
            _transitions[i].active = false;
        }
        
        Serial.print(DEVICE_TYPE);
        Serial.println(" FullColorRGBManager constructor");
    }
    
    // Initialize the RGB manager
    void begin()
    {
        Serial.print(DEVICE_TYPE);
        Serial.println(" Initializing Full-Color RGB Manager...");
        
        // Initialize all LEDs to off
        for (int i = 0; i < _switchCount; i++)
        {
            setColor(i, COLOR_OFF);
        }
        
        Serial.print(DEVICE_TYPE);
        Serial.println(" Full-Color RGB Manager initialized");
        Serial.print("Switches: ");
        Serial.print(_switchCount);
        Serial.print(", Smooth transitions: ");
        Serial.println(_smoothTransitions ? "ON" : "OFF");
    }
    
    // Set RGB color directly
    void setColor(uint8_t switchIndex, uint8_t r, uint8_t g, uint8_t b, bool immediate = false)
    {
        setColor(switchIndex, RGBColor(r, g, b), immediate);
    }
    
    void setColor(uint8_t switchIndex, const RGBColor& color, bool immediate = false)
    {
        if (switchIndex >= _switchCount) return;
        
        if (immediate || !_smoothTransitions)
        {
            _currentColors[switchIndex] = color;
            _targetColors[switchIndex] = color;
            _transitions[switchIndex].active = false;
            updateHardwareRGB(switchIndex, color);
        }
        else
        {
            startTransition(switchIndex, color, _defaultTransitionTime);
        }
    }
    
    // Set color using HSV
    void setColorHSV(uint8_t switchIndex, uint16_t hue, uint8_t saturation, uint8_t value, bool immediate = false)
    {
        HSVColor hsv(hue, saturation, value);
        RGBColor rgb = hsvToRgb(hsv);
        setColor(switchIndex, rgb, immediate);
    }
    
    // Color presets
    void setColorPreset(uint8_t switchIndex, const char* colorName, bool immediate = false)
    {
        RGBColor color = COLOR_OFF;
        
        if (strcmp(colorName, "red") == 0) color = COLOR_RED;
        else if (strcmp(colorName, "green") == 0) color = COLOR_GREEN;
        else if (strcmp(colorName, "blue") == 0) color = COLOR_BLUE;
        else if (strcmp(colorName, "white") == 0) color = COLOR_WHITE;
        else if (strcmp(colorName, "yellow") == 0) color = COLOR_YELLOW;
        else if (strcmp(colorName, "cyan") == 0) color = COLOR_CYAN;
        else if (strcmp(colorName, "magenta") == 0) color = COLOR_MAGENTA;
        else if (strcmp(colorName, "orange") == 0) color = COLOR_ORANGE;
        else if (strcmp(colorName, "purple") == 0) color = COLOR_PURPLE;
        else if (strcmp(colorName, "warm_white") == 0) color = COLOR_WARM_WHITE;
        else if (strcmp(colorName, "cool_white") == 0) color = COLOR_COOL_WHITE;
        else if (strcmp(colorName, "off") == 0) color = COLOR_OFF;
        
        setColor(switchIndex, color, immediate);
        
        Serial.print(DEVICE_TYPE);
        Serial.print(" RGB");
        Serial.print(switchIndex + 1);
        Serial.print(" set to ");
        Serial.println(colorName);
    }
    
    // Start smooth transition
    void startTransition(uint8_t switchIndex, const RGBColor& targetColor, unsigned long duration)
    {
        if (switchIndex >= _switchCount) return;
        
        _transitions[switchIndex].startColor = _currentColors[switchIndex];
        _transitions[switchIndex].targetColor = targetColor;
        _transitions[switchIndex].currentColor = _currentColors[switchIndex];
        _transitions[switchIndex].startTime = millis();
        _transitions[switchIndex].duration = duration;
        _transitions[switchIndex].active = true;
        _transitions[switchIndex].switchIndex = switchIndex;
        
        _targetColors[switchIndex] = targetColor;
        
        Serial.print(DEVICE_TYPE);
        Serial.print(" Starting RGB transition for switch ");
        Serial.print(switchIndex + 1);
        Serial.print(" over ");
        Serial.print(duration);
        Serial.println("ms");
    }
    
    // Update transitions (call in main loop)
    void update()
    {
        for (int i = 0; i < _switchCount; i++)
        {
            if (_transitions[i].active)
            {
                unsigned long elapsed = millis() - _transitions[i].startTime;
                
                if (elapsed >= _transitions[i].duration)
                {
                    // Transition complete
                    _transitions[i].active = false;
                    _currentColors[i] = _transitions[i].targetColor;
                    updateHardwareRGB(i, _currentColors[i]);
                }
                else
                {
                    // Calculate progress and interpolate
                    float progress = (float)elapsed / _transitions[i].duration;
                    _transitions[i].currentColor = interpolateColor(
                        _transitions[i].startColor,
                        _transitions[i].targetColor,
                        progress
                    );
                    _currentColors[i] = _transitions[i].currentColor;
                    updateHardwareRGB(i, _currentColors[i]);
                }
            }
        }
    }
    
    // Brightness control
    void setGlobalBrightness(uint8_t brightness)
    {
        _globalBrightness = brightness;
        
        // Update all current colors
        for (int i = 0; i < _switchCount; i++)
        {
            updateHardwareRGB(i, _currentColors[i]);
        }
        
        Serial.print(DEVICE_TYPE);
        Serial.print(" Global brightness set to ");
        Serial.print((brightness * 100) / 255);
        Serial.println("%");
    }
    
    void setSwitchBrightness(uint8_t switchIndex, uint8_t brightness)
    {
        if (switchIndex >= _switchCount) return;
        
        _switchBrightness[switchIndex] = brightness;
        updateHardwareRGB(switchIndex, _currentColors[switchIndex]);
        
        Serial.print(DEVICE_TYPE);
        Serial.print(" RGB");
        Serial.print(switchIndex + 1);
        Serial.print(" brightness set to ");
        Serial.print((brightness * 100) / 255);
        Serial.println("%");
    }
    
    // Get current color
    RGBColor getCurrentColor(uint8_t switchIndex)
    {
        if (switchIndex >= _switchCount) return COLOR_OFF;
        return _currentColors[switchIndex];
    }
    
    // Enable/disable smooth transitions
    void setSmoothTransitions(bool enabled)
    {
        _smoothTransitions = enabled;
        Serial.print(DEVICE_TYPE);
        Serial.print(" Smooth RGB transitions ");
        Serial.println(enabled ? "enabled" : "disabled");
    }
    
    // Set default transition time
    void setDefaultTransitionTime(unsigned long milliseconds)
    {
        _defaultTransitionTime = milliseconds;
        Serial.print(DEVICE_TYPE);
        Serial.print(" Default RGB transition time set to ");
        Serial.print(milliseconds);
        Serial.println("ms");
    }
    
    // Turn off all RGB
    void turnOffAll(bool immediate = false)
    {
        for (int i = 0; i < _switchCount; i++)
        {
            setColor(i, COLOR_OFF, immediate);
        }
        
        Serial.print(DEVICE_TYPE);
        Serial.println(" All RGB lights turned off");
    }
    
    // Get status
    String getStatus()
    {
        String status = "Brightness: " + String((_globalBrightness * 100) / 255) + "%";
        status += ", Transitions: " + String(_smoothTransitions ? "ON" : "OFF");
        status += ", Default time: " + String(_defaultTransitionTime) + "ms";
        
        return status;
    }
    
    // Get switch status
    String getSwitchStatus(uint8_t switchIndex)
    {
        if (switchIndex >= _switchCount) return "Invalid switch";
        
        RGBColor color = _currentColors[switchIndex];
        String status = "RGB" + String(switchIndex + 1) + ": ";
        status += "R=" + String(color.r) + " G=" + String(color.g) + " B=" + String(color.b);
        status += ", Brightness=" + String((_switchBrightness[switchIndex] * 100) / 255) + "%";
        status += ", Transition=" + String(_transitions[switchIndex].active ? "ACTIVE" : "IDLE");
        
        return status;
    }
};

// Color preset definitions
const FullColorRGBManager::RGBColor FullColorRGBManager::COLOR_OFF(0, 0, 0);
const FullColorRGBManager::RGBColor FullColorRGBManager::COLOR_RED(255, 0, 0);
const FullColorRGBManager::RGBColor FullColorRGBManager::COLOR_GREEN(0, 255, 0);
const FullColorRGBManager::RGBColor FullColorRGBManager::COLOR_BLUE(0, 0, 255);
const FullColorRGBManager::RGBColor FullColorRGBManager::COLOR_WHITE(255, 255, 255);
const FullColorRGBManager::RGBColor FullColorRGBManager::COLOR_YELLOW(255, 255, 0);
const FullColorRGBManager::RGBColor FullColorRGBManager::COLOR_CYAN(0, 255, 255);
const FullColorRGBManager::RGBColor FullColorRGBManager::COLOR_MAGENTA(255, 0, 255);
const FullColorRGBManager::RGBColor FullColorRGBManager::COLOR_ORANGE(255, 165, 0);
const FullColorRGBManager::RGBColor FullColorRGBManager::COLOR_PURPLE(128, 0, 128);
const FullColorRGBManager::RGBColor FullColorRGBManager::COLOR_WARM_WHITE(255, 220, 180);
const FullColorRGBManager::RGBColor FullColorRGBManager::COLOR_COOL_WHITE(180, 220, 255);

#endif // COMPILE_FULL_COLOR_RGB

#endif // FULL_COLOR_RGB_MANAGER_H
