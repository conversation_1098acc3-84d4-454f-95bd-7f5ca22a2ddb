#ifndef MQTT_MANAGER_H
#define MQTT_MANAGER_H

#include "DeviceConfig.h"
#include <PubSubClient.h>
#include "DeviceManager.h"
// Platform-specific includes, EEPROM layout, and MQTT defaults are now handled in DeviceConfig.h

// MQTT topics
#define TOPIC_PREFIX "home/switches/"
#define TOPIC_SUFFIX_SET "/set"
#define TOPIC_SUFFIX_STATE "/state"
#define TOPIC_SUFFIX_AVAILABLE "/available"
#define TOPIC_SUFFIX_NAME "/name"
#define TOPIC_SUFFIX_NAME_SET "/name/set"
#define TOPIC_SUFFIX_RGB_OFF_SET "/rgb/off/set"
#define TOPIC_SUFFIX_RGB_ON_SET "/rgb/on/set"
#define TOPIC_SUFFIX_RGB_OFF_STATE "/rgb/off/state"
#define TOPIC_SUFFIX_RGB_ON_STATE "/rgb/on/state"
#define TOPIC_ALL "home/switches/all"

class MQTTManager
{
private:
    WiFiClient _wifiClient;
    PubSubClient _mqttClient;
    DeviceManager *_deviceManager;
    String _broker;
    int _port;
    String _username;
    String _password;
    bool _enabled;
    unsigned long _lastConnectionAttempt;
    unsigned long _connectionRetryInterval;
    bool _isConnected;

    // Read string from EEPROM at specified address
    String readStringFromEEPROM(int startAddr, int maxLength)
    {
        char data[maxLength + 1];
        for (int i = 0; i < maxLength; i++)
        {
            data[i] = char(EEPROM.read(startAddr + i));
        }
        data[maxLength] = '\0';
        return String(data);
    }

    // Write string to EEPROM at specified address
    void writeStringToEEPROM(int startAddr, const String &strToWrite, int maxLength)
    {
        for (int i = 0; i < maxLength; i++)
        {
            if (i < strToWrite.length())
            {
                EEPROM.write(startAddr + i, strToWrite[i]);
            }
            else
            {
                EEPROM.write(startAddr + i, 0);
            }
        }
        EEPROM.commit();
    }

    // Load MQTT settings from EEPROM
    void loadFromEEPROM()
    {
        // Check if MQTT settings have been initialized
        if (EEPROM.read(MQTT_ENABLED_ADDR) == 0xFF)
        {
            // First time initialization - save defaults from configuration
            _broker = DEFAULT_MQTT_BROKER;
            _port = DEFAULT_MQTT_PORT;
            _username = DEFAULT_MQTT_USERNAME;
            _password = DEFAULT_MQTT_PASSWORD;
            _enabled = false; // Disabled by default

            saveToEEPROM();
            Serial.print(DEVICE_TYPE);
            Serial.println(" MQTT settings initialized with defaults");
        }
        else
        {
            // Load existing settings
            _broker = readStringFromEEPROM(MQTT_BROKER_ADDR, MQTT_BROKER_SIZE);
            _username = readStringFromEEPROM(MQTT_USERNAME_ADDR, MQTT_USERNAME_SIZE);
            _password = readStringFromEEPROM(MQTT_PASSWORD_ADDR, MQTT_PASSWORD_SIZE);
            _enabled = EEPROM.read(MQTT_ENABLED_ADDR) == 1;

            // Load port (stored as 2 bytes)
            _port = EEPROM.read(MQTT_PORT_ADDR) | (EEPROM.read(MQTT_PORT_ADDR + 1) << 8);

            Serial.print(DEVICE_TYPE);
            Serial.println(" MQTT settings loaded from EEPROM");
        }

        Serial.print(DEVICE_TYPE);
        Serial.print(" MQTT Broker: ");
        Serial.println(_broker);
        Serial.print(DEVICE_TYPE);
        Serial.print(" MQTT Port: ");
        Serial.println(_port);
        Serial.print(DEVICE_TYPE);
        Serial.print(" MQTT Enabled: ");
        Serial.println(_enabled ? "Yes" : "No");
    }

    // Save MQTT settings to EEPROM
    void saveToEEPROM()
    {
        writeStringToEEPROM(MQTT_BROKER_ADDR, _broker, MQTT_BROKER_SIZE);
        writeStringToEEPROM(MQTT_USERNAME_ADDR, _username, MQTT_USERNAME_SIZE);
        writeStringToEEPROM(MQTT_PASSWORD_ADDR, _password, MQTT_PASSWORD_SIZE);
        EEPROM.write(MQTT_ENABLED_ADDR, _enabled ? 1 : 0);

        // Save port (as 2 bytes)
        EEPROM.write(MQTT_PORT_ADDR, _port & 0xFF);
        EEPROM.write(MQTT_PORT_ADDR + 1, (_port >> 8) & 0xFF);

        EEPROM.commit();
        Serial.print(DEVICE_TYPE);
        Serial.println(" MQTT settings saved to EEPROM");
    }

public:
    MQTTManager(DeviceManager *deviceManager)
        : _mqttClient(_wifiClient), _deviceManager(deviceManager), _lastConnectionAttempt(0),
          _connectionRetryInterval(5 * 60 * 1000), _isConnected(false) // 5 minutes retry interval
    {
    }

    // Initialize MQTT manager
    void begin()
    {
        // Load settings from EEPROM
        loadFromEEPROM();

        if (_enabled)
        {
            // Set MQTT server
            _mqttClient.setServer(_broker.c_str(), _port);

            Serial.print(DEVICE_TYPE);
            Serial.println(" MQTT Manager initialized");
        }
        else
        {
            Serial.print(DEVICE_TYPE);
            Serial.println(" MQTT is disabled");
        }
    }

    // Set callback function for incoming messages
    void setCallback(void (*callback)(char *, byte *, unsigned int))
    {
        _mqttClient.setCallback(callback);
    }

    // Connect to MQTT broker
    bool connect()
    {
        if (!_enabled)
        {
            return false;
        }

        if (_mqttClient.connected())
        {
            return true;
        }

        // Don't try to reconnect too frequently
        unsigned long now = millis();
        if (now - _lastConnectionAttempt < _connectionRetryInterval)
        {
            // Only log this occasionally to avoid spam
            static unsigned long lastThrottleLog = 0;
            if (now - lastThrottleLog > 60000) // Log every minute
            {
                lastThrottleLog = now;
                unsigned long remainingTime = (_connectionRetryInterval - (now - _lastConnectionAttempt)) / 1000;
                Serial.print(DEVICE_TYPE);
                Serial.print(" MQTT connection throttled, ");
                Serial.print(remainingTime);
                Serial.println(" seconds remaining");
            }
            return false;
        }
        _lastConnectionAttempt = now;

        Serial.print(DEVICE_TYPE);
        Serial.println(" attempting MQTT connection...");

        // Create a client ID based on the device ID and type
        String clientId = String(DEVICE_TYPE) + "_" + _deviceManager->getDeviceID();

        bool connected = false;
        if (_username.length() > 0)
        {
            connected = _mqttClient.connect(clientId.c_str(), _username.c_str(), _password.c_str());
        }
        else
        {
            connected = _mqttClient.connect(clientId.c_str());
        }

        if (connected)
        {
            _isConnected = true;
            Serial.print(DEVICE_TYPE);
            Serial.println(" MQTT connected");

            // Subscribe to control topics
            subscribeToTopics();

            // Publish device availability
            publishAvailability(true);

            // Publish all current states
            publishAllStates();

            return true;
        }
        else
        {
            _isConnected = false;
            Serial.print(DEVICE_TYPE);
            Serial.print(" MQTT connection failed, rc=");
            Serial.println(_mqttClient.state());
            return false;
        }
    }

    // Main loop function - call this regularly
    void loop()
    {
        if (!_enabled)
        {
            return;
        }

        if (!_mqttClient.connected())
        {
            _isConnected = false;
            connect(); // Try to reconnect
        }
        else
        {
            _isConnected = true;
            _mqttClient.loop(); // Process incoming messages
        }
    }

    // Check if MQTT is connected
    bool isConnected()
    {
        return _isConnected && _mqttClient.connected();
    }

    // Simplified methods for device variant
    void subscribeToTopics() { /* Simplified */ }
    void publishAvailability(bool available) { /* Simplified */ }
    void publishAllStates() { /* Simplified */ }
    void publishDeviceName() { /* Simplified */ }
    void publishSwitchState(uint8_t switchIndex) { /* Simplified */ }
    void publishRGBOffState(uint8_t switchIndex) { /* Simplified */ }
    void publishRGBOnState(uint8_t switchIndex) { /* Simplified */ }

    // Handle incoming MQTT messages (simplified)
    void handleMessage(char *topic, byte *payload, unsigned int length)
    {
        Serial.print(DEVICE_TYPE);
        Serial.println(" MQTT message received (simplified handler)");
    }
};

#endif // MQTT_MANAGER_H
