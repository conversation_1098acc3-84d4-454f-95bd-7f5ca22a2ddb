#ifndef WEB_TEMPLATES_ESP32_H
#define WEB_TEMPLATES_ESP32_H

#include "DeviceConfig.h"

// Only compile ESP32 templates for ESP32 devices
#ifdef IS_ESP32

// ESP32 Switch Control Template (Full-Color RGB + Color Cycling)
const char ESP32_switchesTemplate[] PROGMEM = R"=====(
<div class='content-section'>
<h1>Advanced Switch Control</h1>

<!-- Global RGB Controls -->
<div class='global-controls'>
<h2>Global RGB Settings</h2>
<div class='control-row'>
<div class='control-group'>
<label for='globalBrightness'>Global Brightness</label>
<input type='range' id='globalBrightness' min='0' max='255' value='255' oninput='setGlobalBrightness(this.value)'>
<span id='brightnessValue'>100%</span>
</div>
<div class='control-group'>
<label for='colorCycleMode'>Color Cycle Mode</label>
<select id='colorCycleMode' onchange='setColorCycleMode(this.value)'>
<option value='0'>Off</option>
<option value='1'>Rainbow</option>
<option value='2'>Breathing</option>
<option value='3'>Strobe</option>
<option value='4'>Fade</option>
<option value='5'>Wave</option>
<option value='6'>Fire</option>
<option value='7'>Ocean</option>
<option value='8'>Custom</option>
</select>
</div>
</div>

<div class='control-row' id='cycleControls' style='display:none;'>
<div class='control-group'>
<label for='cycleSpeed'>Speed</label>
<input type='range' id='cycleSpeed' min='1' max='100' value='50' oninput='setCycleSpeed(this.value)'>
<span id='speedValue'>50</span>
</div>
<div class='control-group'>
<label for='cycleSaturation'>Saturation</label>
<input type='range' id='cycleSaturation' min='0' max='100' value='100' oninput='setCycleSaturation(this.value)'>
<span id='saturationValue'>100%</span>
</div>
<div class='control-group'>
<label for='switchMask'>Active Switches</label>
<div class='switch-mask' id='switchMask'></div>
</div>
</div>
</div>

<!-- Individual Switch Controls -->
<div class='switch-grid' id='switchGrid'>
<div class='loading-message'>Loading switches...</div>
</div>
</div>

<script>
let currentColorCycleMode = 0;

function toggleSwitch(i){
    fetch('/switch',{method:'POST',headers:{'Content-Type':'application/x-www-form-urlencoded'},body:'index='+i+'&state='+(document.querySelector('.switch-card:nth-child('+(i+1)+') .switch-state').classList.contains('on')?'0':'1')}).then(()=>{console.log('Switch toggle sent for index:', i);}).catch(e=>console.error('Toggle switch error:', e));
}

function setRGBColor(switchIndex, r, g, b) {
    fetch('/api/rgb/set', {
        method: 'POST',
        headers: {'Content-Type': 'application/json'},
        body: JSON.stringify({switch: switchIndex, r: r, g: g, b: b})
    }).then(response => {
        if (response.ok) {
            console.log('RGB color set for switch', switchIndex, 'to', r, g, b);
        }
    }).catch(e => console.error('RGB color error:', e));
}

function setColorPreset(switchIndex, preset) {
    fetch('/api/rgb/preset', {
        method: 'POST',
        headers: {'Content-Type': 'application/json'},
        body: JSON.stringify({switch: switchIndex, preset: preset})
    }).then(response => {
        if (response.ok) {
            console.log('RGB preset set for switch', switchIndex, 'to', preset);
        }
    }).catch(e => console.error('RGB preset error:', e));
}

function setGlobalBrightness(brightness) {
    document.getElementById('brightnessValue').textContent = Math.round((brightness / 255) * 100) + '%';
    fetch('/api/rgb/brightness', {
        method: 'POST',
        headers: {'Content-Type': 'application/json'},
        body: JSON.stringify({brightness: parseInt(brightness)})
    }).then(response => {
        if (response.ok) {
            console.log('Global brightness set to', brightness);
        }
    }).catch(e => console.error('Brightness error:', e));
}

function setColorCycleMode(mode) {
    currentColorCycleMode = parseInt(mode);
    const cycleControls = document.getElementById('cycleControls');
    
    if (mode === '0') {
        cycleControls.style.display = 'none';
    } else {
        cycleControls.style.display = 'flex';
    }
    
    fetch('/api/rgb/cycle/mode', {
        method: 'POST',
        headers: {'Content-Type': 'application/json'},
        body: JSON.stringify({mode: parseInt(mode)})
    }).then(response => {
        if (response.ok) {
            console.log('Color cycle mode set to', mode);
        }
    }).catch(e => console.error('Cycle mode error:', e));
}

function setCycleSpeed(speed) {
    document.getElementById('speedValue').textContent = speed;
    fetch('/api/rgb/cycle/speed', {
        method: 'POST',
        headers: {'Content-Type': 'application/json'},
        body: JSON.stringify({speed: parseInt(speed)})
    }).then(response => {
        if (response.ok) {
            console.log('Cycle speed set to', speed);
        }
    }).catch(e => console.error('Cycle speed error:', e));
}

function setCycleSaturation(saturation) {
    document.getElementById('saturationValue').textContent = saturation + '%';
    fetch('/api/rgb/cycle/saturation', {
        method: 'POST',
        headers: {'Content-Type': 'application/json'},
        body: JSON.stringify({saturation: parseInt(saturation)})
    }).then(response => {
        if (response.ok) {
            console.log('Cycle saturation set to', saturation);
        }
    }).catch(e => console.error('Cycle saturation error:', e));
}

function toggleSwitchMask(switchIndex) {
    const checkbox = document.getElementById('mask' + switchIndex);
    const mask = getSwitchMask();
    
    fetch('/api/rgb/cycle/mask', {
        method: 'POST',
        headers: {'Content-Type': 'application/json'},
        body: JSON.stringify({mask: mask})
    }).then(response => {
        if (response.ok) {
            console.log('Switch mask updated to', mask);
        }
    }).catch(e => console.error('Switch mask error:', e));
}

function getSwitchMask() {
    let mask = 0;
    const checkboxes = document.querySelectorAll('.switch-mask input[type="checkbox"]');
    checkboxes.forEach((checkbox, index) => {
        if (checkbox.checked) {
            mask |= (1 << index);
        }
    });
    return mask;
}

function updateColorPicker(switchIndex, colorPicker) {
    const hex = colorPicker.value;
    const r = parseInt(hex.substr(1, 2), 16);
    const g = parseInt(hex.substr(3, 2), 16);
    const b = parseInt(hex.substr(5, 2), 16);
    
    setRGBColor(switchIndex, r, g, b);
    
    // Update RGB input fields
    document.getElementById('r' + switchIndex).value = r;
    document.getElementById('g' + switchIndex).value = g;
    document.getElementById('b' + switchIndex).value = b;
}

function updateRGBInputs(switchIndex) {
    const r = parseInt(document.getElementById('r' + switchIndex).value) || 0;
    const g = parseInt(document.getElementById('g' + switchIndex).value) || 0;
    const b = parseInt(document.getElementById('b' + switchIndex).value) || 0;
    
    setRGBColor(switchIndex, r, g, b);
    
    // Update color picker
    const hex = '#' + 
        ('0' + r.toString(16)).slice(-2) + 
        ('0' + g.toString(16)).slice(-2) + 
        ('0' + b.toString(16)).slice(-2);
    document.getElementById('colorPicker' + switchIndex).value = hex;
}

function loadInitialSwitches(){
    fetch('/api/status').then(r=>{
        if(!r.ok) throw new Error('Status API failed: ' + r.status);
        return r.json();
    }).then(d=>{
        let html = '';
        let maskHtml = '';
        
        for(let i = 0; i < d.switchCount; i++){
            const state = d.switchState[i];
            
            // Switch mask checkbox
            maskHtml += '<label><input type="checkbox" id="mask' + i + '" onchange="toggleSwitchMask(' + i + ')" checked> SW' + (i+1) + '</label>';
            
            // Switch card
            html += '<div class="switch-card esp32"><div class="switch-header">';
            html += '<div class="switch-title">Switch ' + (i+1) + '</div>';
            html += '<div class="switch-state ' + (state ? 'on' : 'off') + '">' + (state ? 'ON' : 'OFF') + '</div></div>';
            html += '<div class="toggle-container"><span>Toggle Switch</span>';
            html += '<div class="toggle-switch ' + (state ? 'on' : '') + '" onclick="toggleSwitch(' + i + ')"></div></div>';
            
            // RGB Controls
            html += '<div class="rgb-controls">';
            html += '<h4>RGB Control</h4>';
            
            // Color picker
            html += '<div class="color-picker-group">';
            html += '<label>Color Picker:</label>';
            html += '<input type="color" id="colorPicker' + i + '" value="#000000" onchange="updateColorPicker(' + i + ', this)">';
            html += '</div>';
            
            // RGB input fields
            html += '<div class="rgb-inputs">';
            html += '<div class="rgb-input"><label>R:</label><input type="number" id="r' + i + '" min="0" max="255" value="0" onchange="updateRGBInputs(' + i + ')"></div>';
            html += '<div class="rgb-input"><label>G:</label><input type="number" id="g' + i + '" min="0" max="255" value="0" onchange="updateRGBInputs(' + i + ')"></div>';
            html += '<div class="rgb-input"><label>B:</label><input type="number" id="b' + i + '" min="0" max="255" value="0" onchange="updateRGBInputs(' + i + ')"></div>';
            html += '</div>';
            
            // Preset colors
            html += '<div class="preset-colors">';
            html += '<button onclick="setColorPreset(' + i + ', \'red\')">Red</button>';
            html += '<button onclick="setColorPreset(' + i + ', \'green\')">Green</button>';
            html += '<button onclick="setColorPreset(' + i + ', \'blue\')">Blue</button>';
            html += '<button onclick="setColorPreset(' + i + ', \'white\')">White</button>';
            html += '<button onclick="setColorPreset(' + i + ', \'off\')">Off</button>';
            html += '</div>';
            
            html += '</div></div>';
        }
        
        document.getElementById('switchGrid').innerHTML = html;
        document.getElementById('switchMask').innerHTML = maskHtml;
        
    }).catch(e=>{
        console.error('Load switches error:', e);
        document.getElementById('switchGrid').innerHTML = '<div class="error-message">Failed to load switches</div>';
    });
}

window.addEventListener('load', function() {
    setActiveNav('/switches');
    loadInitialSwitches();
});
</script>
)=====";

// ESP32 CSS for advanced RGB controls
const char ESP32_switchesCSS[] PROGMEM = R"=====(
.global-controls {
    background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
    color: white;
    padding: 20px;
    border-radius: 12px;
    margin-bottom: 20px;
    box-shadow: 0 4px 15px rgba(0,0,0,0.1);
}

.global-controls h2 {
    margin: 0 0 15px 0;
    font-size: 18px;
    font-weight: 600;
}

.control-row {
    display: flex;
    gap: 20px;
    align-items: center;
    flex-wrap: wrap;
    margin-bottom: 15px;
}

.control-row:last-child {
    margin-bottom: 0;
}

.control-group {
    display: flex;
    flex-direction: column;
    gap: 5px;
    min-width: 150px;
}

.control-group label {
    font-size: 14px;
    font-weight: 500;
    opacity: 0.9;
}

.control-group input[type="range"] {
    width: 100%;
    height: 6px;
    border-radius: 3px;
    background: rgba(255,255,255,0.3);
    outline: none;
    -webkit-appearance: none;
}

.control-group input[type="range"]::-webkit-slider-thumb {
    -webkit-appearance: none;
    appearance: none;
    width: 18px;
    height: 18px;
    border-radius: 50%;
    background: white;
    cursor: pointer;
    box-shadow: 0 2px 6px rgba(0,0,0,0.2);
}

.control-group select {
    padding: 8px 12px;
    border: none;
    border-radius: 6px;
    background: rgba(255,255,255,0.9);
    font-size: 14px;
    cursor: pointer;
}

.switch-mask {
    display: flex;
    gap: 10px;
    flex-wrap: wrap;
}

.switch-mask label {
    display: flex;
    align-items: center;
    gap: 5px;
    background: rgba(255,255,255,0.2);
    padding: 5px 10px;
    border-radius: 15px;
    font-size: 12px;
    cursor: pointer;
    transition: background 0.3s ease;
}

.switch-mask label:hover {
    background: rgba(255,255,255,0.3);
}

.switch-mask input[type="checkbox"] {
    margin: 0;
}

.switch-card.esp32 {
    background: linear-gradient(135deg, #f5f7fa 0%, #c3cfe2 100%);
    border: none;
    box-shadow: 0 4px 15px rgba(0,0,0,0.1);
}

.rgb-controls {
    margin-top: 15px;
    padding: 15px;
    background: rgba(255,255,255,0.7);
    border-radius: 8px;
}

.rgb-controls h4 {
    margin: 0 0 12px 0;
    font-size: 14px;
    color: #333;
    font-weight: 600;
}

.color-picker-group {
    display: flex;
    align-items: center;
    gap: 10px;
    margin-bottom: 12px;
}

.color-picker-group label {
    font-size: 13px;
    color: #666;
    min-width: 80px;
}

.color-picker-group input[type="color"] {
    width: 50px;
    height: 35px;
    border: none;
    border-radius: 6px;
    cursor: pointer;
    background: none;
}

.rgb-inputs {
    display: flex;
    gap: 10px;
    margin-bottom: 12px;
}

.rgb-input {
    display: flex;
    flex-direction: column;
    gap: 3px;
    flex: 1;
}

.rgb-input label {
    font-size: 12px;
    color: #666;
    font-weight: 500;
}

.rgb-input input[type="number"] {
    padding: 6px 8px;
    border: 1px solid #ddd;
    border-radius: 4px;
    font-size: 13px;
    text-align: center;
}

.preset-colors {
    display: flex;
    gap: 6px;
    flex-wrap: wrap;
}

.preset-colors button {
    padding: 6px 12px;
    border: none;
    border-radius: 15px;
    font-size: 12px;
    cursor: pointer;
    transition: all 0.3s ease;
    font-weight: 500;
}

.preset-colors button:nth-child(1) { background: #ff4757; color: white; }
.preset-colors button:nth-child(2) { background: #2ed573; color: white; }
.preset-colors button:nth-child(3) { background: #3742fa; color: white; }
.preset-colors button:nth-child(4) { background: #f1f2f6; color: #333; border: 1px solid #ddd; }
.preset-colors button:nth-child(5) { background: #57606f; color: white; }

.preset-colors button:hover {
    transform: translateY(-1px);
    box-shadow: 0 2px 8px rgba(0,0,0,0.2);
}

/* Color Cycle Controls */
.cycle-config-panel {
    background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
    color: white;
    padding: 25px;
    border-radius: 15px;
    margin-bottom: 25px;
    box-shadow: 0 6px 20px rgba(0,0,0,0.15);
}

.config-grid {
    display: grid;
    grid-template-columns: repeat(auto-fit, minmax(200px, 1fr));
    gap: 20px;
    margin-bottom: 20px;
}

.config-group {
    display: flex;
    flex-direction: column;
    gap: 8px;
}

.config-group label {
    font-weight: 600;
    font-size: 14px;
    opacity: 0.95;
}

.config-group select {
    padding: 10px 15px;
    border: none;
    border-radius: 8px;
    background: rgba(255,255,255,0.9);
    font-size: 14px;
    cursor: pointer;
}

.config-group input[type="range"] {
    width: 100%;
    height: 8px;
    border-radius: 4px;
    background: rgba(255,255,255,0.3);
    outline: none;
    -webkit-appearance: none;
}

.switch-selection, .preset-sequences, .cycle-status {
    background: #f8f9fa;
    padding: 20px;
    border-radius: 12px;
    margin-bottom: 20px;
    box-shadow: 0 2px 10px rgba(0,0,0,0.1);
}

.switch-checkboxes {
    display: flex;
    gap: 15px;
    flex-wrap: wrap;
}

.switch-checkboxes label {
    display: flex;
    align-items: center;
    gap: 8px;
    background: #e9ecef;
    padding: 8px 15px;
    border-radius: 20px;
    font-size: 14px;
    cursor: pointer;
    transition: background 0.3s ease;
}

.switch-checkboxes label:hover {
    background: #dee2e6;
}

.preset-buttons {
    display: grid;
    grid-template-columns: repeat(auto-fit, minmax(120px, 1fr));
    gap: 12px;
}

.preset-buttons button {
    padding: 12px 20px;
    border: none;
    border-radius: 8px;
    font-weight: 600;
    cursor: pointer;
    transition: all 0.3s ease;
    background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
    color: white;
}

.preset-buttons button:hover {
    transform: translateY(-2px);
    box-shadow: 0 4px 15px rgba(102, 126, 234, 0.4);
}

.status-info {
    display: grid;
    grid-template-columns: repeat(auto-fit, minmax(150px, 1fr));
    gap: 15px;
}

.status-info > div {
    background: #e9ecef;
    padding: 10px 15px;
    border-radius: 8px;
    font-weight: 500;
}

@media (max-width: 768px) {
    .control-row {
        flex-direction: column;
        align-items: stretch;
        gap: 15px;
    }

    .control-group {
        min-width: auto;
    }

    .rgb-inputs {
        flex-direction: column;
        gap: 8px;
    }

    .switch-mask {
        justify-content: center;
    }

    .config-grid {
        grid-template-columns: 1fr;
    }

    .switch-checkboxes {
        justify-content: center;
    }

    .preset-buttons {
        grid-template-columns: repeat(2, 1fr);
    }
}
)=====";

// ESP32 Advanced Color Cycle Controls Template
const char ESP32_colorCycleTemplate[] PROGMEM = R"=====(
<div class='content-section'>
<h1>Advanced Color Cycle Control</h1>

<!-- Color Cycle Configuration -->
<div class='cycle-config-panel'>
<h2>Color Cycle Configuration</h2>

<div class='config-grid'>
<div class='config-group'>
<label for='cycleMode'>Cycle Mode</label>
<select id='cycleMode' onchange='updateCycleMode(this.value)'>
<option value='0'>Off</option>
<option value='1'>Rainbow</option>
<option value='2'>Breathing</option>
<option value='3'>Strobe</option>
<option value='4'>Fade</option>
<option value='5'>Wave</option>
<option value='6'>Fire</option>
<option value='7'>Ocean</option>
<option value='8'>Custom Sequence</option>
</select>
</div>

<div class='config-group'>
<label for='cycleSpeed'>Speed: <span id='speedDisplay'>50</span></label>
<input type='range' id='cycleSpeed' min='1' max='100' value='50' oninput='updateCycleSpeed(this.value)'>
</div>

<div class='config-group'>
<label for='cycleBrightness'>Brightness: <span id='brightnessDisplay'>100%</span></label>
<input type='range' id='cycleBrightness' min='0' max='255' value='255' oninput='updateCycleBrightness(this.value)'>
</div>

<div class='config-group'>
<label for='cycleSaturation'>Saturation: <span id='saturationDisplay'>100%</span></label>
<input type='range' id='cycleSaturation' min='0' max='100' value='100' oninput='updateCycleSaturation(this.value)'>
</div>
</div>

<!-- Switch Selection -->
<div class='switch-selection'>
<h3>Active Switches</h3>
<div class='switch-checkboxes' id='switchCheckboxes'>
<!-- Populated by JavaScript -->
</div>
</div>

<!-- Preset Sequences -->
<div class='preset-sequences'>
<h3>Preset Sequences</h3>
<div class='preset-buttons'>
<button onclick='loadPreset("sunset")'>Sunset</button>
<button onclick='loadPreset("ocean")'>Ocean Waves</button>
<button onclick='loadPreset("forest")'>Forest</button>
<button onclick='loadPreset("party")'>Party</button>
<button onclick='loadPreset("relax")'>Relax</button>
<button onclick='loadPreset("energize")'>Energize</button>
</div>
</div>

<!-- Real-time Status -->
<div class='cycle-status'>
<h3>Current Status</h3>
<div class='status-info' id='statusInfo'>
<div>Mode: <span id='currentMode'>Off</span></div>
<div>Speed: <span id='currentSpeed'>50</span></div>
<div>Active Switches: <span id='activeSwitches'>All</span></div>
</div>
</div>
</div>
</div>

<script>
function updateCycleMode(mode) {
    const currentMode = document.getElementById('currentMode');
    const modes = ['Off', 'Rainbow', 'Breathing', 'Strobe', 'Fade', 'Wave', 'Fire', 'Ocean', 'Custom'];
    currentMode.textContent = modes[mode] || 'Unknown';

    fetch('/api/rgb/cycle/mode', {
        method: 'POST',
        headers: {'Content-Type': 'application/json'},
        body: JSON.stringify({mode: parseInt(mode)})
    }).catch(e => console.error('Mode update error:', e));
}

function updateCycleSpeed(speed) {
    document.getElementById('speedDisplay').textContent = speed;
    document.getElementById('currentSpeed').textContent = speed;

    fetch('/api/rgb/cycle/speed', {
        method: 'POST',
        headers: {'Content-Type': 'application/json'},
        body: JSON.stringify({speed: parseInt(speed)})
    }).catch(e => console.error('Speed update error:', e));
}

function updateCycleBrightness(brightness) {
    const percent = Math.round((brightness / 255) * 100);
    document.getElementById('brightnessDisplay').textContent = percent + '%';

    fetch('/api/rgb/cycle/brightness', {
        method: 'POST',
        headers: {'Content-Type': 'application/json'},
        body: JSON.stringify({brightness: parseInt(brightness)})
    }).catch(e => console.error('Brightness update error:', e));
}

function updateCycleSaturation(saturation) {
    document.getElementById('saturationDisplay').textContent = saturation + '%';

    fetch('/api/rgb/cycle/saturation', {
        method: 'POST',
        headers: {'Content-Type': 'application/json'},
        body: JSON.stringify({saturation: parseInt(saturation)})
    }).catch(e => console.error('Saturation update error:', e));
}

function updateSwitchMask() {
    let mask = 0;
    const checkboxes = document.querySelectorAll('.switch-checkboxes input[type="checkbox"]');
    const activeList = [];

    checkboxes.forEach((checkbox, index) => {
        if (checkbox.checked) {
            mask |= (1 << index);
            activeList.push('SW' + (index + 1));
        }
    });

    document.getElementById('activeSwitches').textContent = activeList.length > 0 ? activeList.join(', ') : 'None';

    fetch('/api/rgb/cycle/mask', {
        method: 'POST',
        headers: {'Content-Type': 'application/json'},
        body: JSON.stringify({mask: mask})
    }).catch(e => console.error('Mask update error:', e));
}

function loadPreset(presetName) {
    const presets = {
        sunset: {mode: 6, speed: 30, saturation: 90},
        ocean: {mode: 7, speed: 40, saturation: 85},
        forest: {mode: 1, speed: 20, saturation: 70},
        party: {mode: 3, speed: 80, saturation: 100},
        relax: {mode: 2, speed: 15, saturation: 60},
        energize: {mode: 5, speed: 70, saturation: 95}
    };

    const preset = presets[presetName];
    if (preset) {
        document.getElementById('cycleMode').value = preset.mode;
        document.getElementById('cycleSpeed').value = preset.speed;
        document.getElementById('cycleSaturation').value = preset.saturation;

        updateCycleMode(preset.mode);
        updateCycleSpeed(preset.speed);
        updateCycleSaturation(preset.saturation);
    }
}

function initializePage() {
    fetch('/api/status').then(r => r.json()).then(data => {
        const checkboxContainer = document.getElementById('switchCheckboxes');
        for (let i = 0; i < data.switchCount; i++) {
            const label = document.createElement('label');
            label.innerHTML = `
                <input type="checkbox" checked onchange="updateSwitchMask()">
                Switch ${i + 1}
            `;
            checkboxContainer.appendChild(label);
        }
        updateSwitchMask();
    }).catch(e => console.error('Status load error:', e));
}

window.addEventListener('load', function() {
    setActiveNav('/color-cycle');
    initializePage();
});
</script>
)=====";

#endif // IS_ESP32

#endif // WEB_TEMPLATES_ESP32_H
