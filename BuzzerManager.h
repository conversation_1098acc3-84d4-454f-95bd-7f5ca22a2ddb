#ifndef BUZZER_MANAGER_H
#define BUZZER_MANAGER_H

#include "DeviceConfig.h"

// Only compile this library for devices with buzzer
#ifdef COMPILE_BUZZER

class BuzzerManager
{
private:
    uint8_t _buzzerPin;
    bool _initialized;
    bool _isPlaying;
    unsigned long _playStartTime;
    unsigned long _playDuration;

    // Tone definitions (frequencies in Hz)
    static const int TONE_LOW = 200;
    static const int TONE_MID = 500;
    static const int TONE_HIGH = 1000;
    static const int TONE_BEEP = 800;
    static const int TONE_ALERT = 1200;
    static const int TONE_ERROR = 150;
    static const int TONE_SUCCESS = 1500;

    // Pattern timing
    static const int SHORT_BEEP = 100;
    static const int LONG_BEEP = 300;
    static const int PAUSE_SHORT = 100;
    static const int PAUSE_LONG = 500;

    // Current pattern state
    struct Pattern
    {
        int *frequencies;
        int *durations;
        int *pauses;
        int stepCount;
        int currentStep;
        bool repeat;
        unsigned long stepStartTime;
        bool inPause;
    } _currentPattern;

    // Note: Complex pattern arrays could be added here if needed
    // For now, using simple tone sequences in methods

public:
    BuzzerManager()
        : _buzzerPin(BUZZER_PIN), _initialized(false), _isPlaying(false),
          _playStartTime(0), _playDuration(0)
    {
        // Initialize pattern structure
        _currentPattern.frequencies = nullptr;
        _currentPattern.durations = nullptr;
        _currentPattern.pauses = nullptr;
        _currentPattern.stepCount = 0;
        _currentPattern.currentStep = 0;
        _currentPattern.repeat = false;
        _currentPattern.stepStartTime = 0;
        _currentPattern.inPause = false;

        Serial.print(DEVICE_TYPE);
        Serial.println(" Buzzer Manager constructor");
    }

    // Initialize the buzzer
    bool begin()
    {
        Serial.print(DEVICE_TYPE);
        Serial.println(" Initializing buzzer...");

        // Configure buzzer pin as output
        pinMode(_buzzerPin, OUTPUT);
        digitalWrite(_buzzerPin, LOW);

        _initialized = true;

        Serial.print(DEVICE_TYPE);
        Serial.println(" Buzzer initialized successfully");
        Serial.print("Buzzer Pin: ");
        Serial.println(_buzzerPin);

        // Play startup sound
        playStartupSound();

        return true;
    }

    // Play a simple tone
    void playTone(int frequency, int duration)
    {
        if (!_initialized)
            return;

        tone(_buzzerPin, frequency, duration);
        _isPlaying = true;
        _playStartTime = millis();
        _playDuration = duration;

        Serial.print(DEVICE_TYPE);
        Serial.print(" Playing tone: ");
        Serial.print(frequency);
        Serial.print("Hz for ");
        Serial.print(duration);
        Serial.println("ms");
    }

    // Play predefined sounds
    void playStartupSound()
    {
        if (!_initialized)
            return;

        Serial.print(DEVICE_TYPE);
        Serial.println(" Playing startup sound");

        // Rising tone sequence
        playTone(TONE_LOW, SHORT_BEEP);
        delay(PAUSE_SHORT);
        playTone(TONE_MID, SHORT_BEEP);
        delay(PAUSE_SHORT);
        playTone(TONE_HIGH, LONG_BEEP);
    }

    void playErrorSound()
    {
        if (!_initialized)
            return;

        Serial.print(DEVICE_TYPE);
        Serial.println(" Playing error sound");

        // Low frequency error beeps
        for (int i = 0; i < 3; i++)
        {
            playTone(TONE_ERROR, SHORT_BEEP);
            delay(PAUSE_SHORT);
        }
    }

    void playSuccessSound()
    {
        if (!_initialized)
            return;

        Serial.print(DEVICE_TYPE);
        Serial.println(" Playing success sound");

        // High frequency success beeps
        playTone(TONE_SUCCESS, SHORT_BEEP);
        delay(PAUSE_SHORT);
        playTone(TONE_SUCCESS, SHORT_BEEP);
    }

    void playAlertSound()
    {
        if (!_initialized)
            return;

        Serial.print(DEVICE_TYPE);
        Serial.println(" Playing alert sound");

        // Alternating high-low alert
        for (int i = 0; i < 5; i++)
        {
            playTone(TONE_ALERT, SHORT_BEEP);
            delay(PAUSE_SHORT);
            playTone(TONE_LOW, SHORT_BEEP);
            delay(PAUSE_SHORT);
        }
    }

    void playBeep()
    {
        if (!_initialized)
            return;

        playTone(TONE_BEEP, SHORT_BEEP);
    }

    void playDoubleBeep()
    {
        if (!_initialized)
            return;

        playTone(TONE_BEEP, SHORT_BEEP);
        delay(PAUSE_SHORT);
        playTone(TONE_BEEP, SHORT_BEEP);
    }

    void playTripleBeep()
    {
        if (!_initialized)
            return;

        for (int i = 0; i < 3; i++)
        {
            playTone(TONE_BEEP, SHORT_BEEP);
            if (i < 2)
                delay(PAUSE_SHORT);
        }
    }

    // Temperature-related sounds
    void playTemperatureAlert()
    {
        if (!_initialized)
            return;

        Serial.print(DEVICE_TYPE);
        Serial.println(" Playing temperature alert");

        // Rapid beeping for temperature alert
        for (int i = 0; i < 10; i++)
        {
            playTone(TONE_ALERT, 50);
            delay(50);
        }
    }

    void playConnectionSound()
    {
        if (!_initialized)
            return;

        Serial.print(DEVICE_TYPE);
        Serial.println(" Playing connection sound");

        // Rising tone for connection
        playTone(TONE_LOW, SHORT_BEEP);
        delay(50);
        playTone(TONE_MID, SHORT_BEEP);
        delay(50);
        playTone(TONE_HIGH, SHORT_BEEP);
    }

    void playDisconnectionSound()
    {
        if (!_initialized)
            return;

        Serial.print(DEVICE_TYPE);
        Serial.println(" Playing disconnection sound");

        // Falling tone for disconnection
        playTone(TONE_HIGH, SHORT_BEEP);
        delay(50);
        playTone(TONE_MID, SHORT_BEEP);
        delay(50);
        playTone(TONE_LOW, SHORT_BEEP);
    }

    // RF-related sounds
    void playRFReceived()
    {
        if (!_initialized)
            return;

        playTone(TONE_MID, 50);
    }

    void playRFCommand(int commandType)
    {
        if (!_initialized)
            return;

        Serial.print(DEVICE_TYPE);
        Serial.print(" Playing RF command sound: ");
        Serial.println(commandType);

        // Different tones for different command types
        switch (commandType)
        {
        case 1: // Switch command
            playTone(TONE_BEEP, SHORT_BEEP);
            break;
        case 2: // Scene command
            playDoubleBeep();
            break;
        case 3: // System command
            playTripleBeep();
            break;
        default:
            playBeep();
            break;
        }
    }

    // Stop any playing sound
    void stop()
    {
        if (!_initialized)
            return;

        noTone(_buzzerPin);
        digitalWrite(_buzzerPin, LOW);
        _isPlaying = false;

        Serial.print(DEVICE_TYPE);
        Serial.println(" Buzzer stopped");
    }

    // Check if buzzer is currently playing
    bool isPlaying()
    {
        if (!_initialized)
            return false;

        // Check if play duration has elapsed
        if (_isPlaying && millis() - _playStartTime >= _playDuration)
        {
            _isPlaying = false;
        }

        return _isPlaying;
    }

    // Update method to be called in main loop for pattern management
    void update()
    {
        if (!_initialized)
            return;

        // Update playing state
        isPlaying();

        // Handle pattern playback if implemented
        // This could be expanded for complex pattern sequences
    }

    // Test all sounds
    void testAllSounds()
    {
        if (!_initialized)
            return;

        Serial.print(DEVICE_TYPE);
        Serial.println(" Testing all buzzer sounds...");

        playStartupSound();
        delay(1000);

        playSuccessSound();
        delay(1000);

        playErrorSound();
        delay(1000);

        playAlertSound();
        delay(1000);

        playConnectionSound();
        delay(1000);

        playDisconnectionSound();
        delay(1000);

        Serial.print(DEVICE_TYPE);
        Serial.println(" Buzzer test complete");
    }

    // Get buzzer status
    String getStatus()
    {
        if (!_initialized)
        {
            return "Not initialized";
        }

        String status = "Pin: " + String(_buzzerPin);
        status += ", Playing: " + String(_isPlaying ? "Yes" : "No");

        return status;
    }
};

#endif // COMPILE_BUZZER

#endif // BUZZER_MANAGER_H
