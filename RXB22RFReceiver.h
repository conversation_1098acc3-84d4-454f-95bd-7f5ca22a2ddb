#ifndef RXB22_RF_RECEIVER_H
#define RXB22_RF_RECEIVER_H

#include "DeviceConfig.h"

// Only compile this library for devices with RF receiver
#ifdef COMPILE_RF_RECEIVER

class RXB22RFReceiver
{
private:
    uint8_t _dataPin;
    bool _initialized;

    // RF protocol parameters
    static const unsigned long PULSE_LENGTH = 350;  // microseconds
    static const unsigned long SYNC_LENGTH = 10000; // microseconds
    static const uint8_t REPEAT_TRANSMIT = 3;
    static const uint8_t MAX_CHANGES = 67;

    // Timing tolerances
    static const uint8_t TOLERANCE = 60; // percent

    // Buffer for received data
    volatile unsigned long _changeBuffer[MAX_CHANGES];
    volatile uint8_t _changeCount;
    volatile unsigned long _lastTime;
    volatile bool _dataReceived;
    volatile unsigned long _receivedCode;
    volatile unsigned int _receivedBitLength;

    // Callback function for received data
    void (*_receiveCallback)(unsigned long code, unsigned int bitLength);

    // Static instance for interrupt handling
    static RXB22RFR<PERSON>eiver *_instance;

    // Interrupt service routine
    static void IRAM_ATTR handleInterrupt()
    {
        if (_instance)
        {
            _instance->handleReceive();
        }
    }

    // Handle received data in interrupt
    void IRAM_ATTR handleReceive()
    {
        unsigned long time = micros();
        unsigned long duration = time - _lastTime;

        if (duration > 5000 && duration > _changeBuffer[_changeCount - 1] + 3000 && _changeCount >= 6)
        {
            // End of transmission detected
            processReceivedData();
            _changeCount = 0;
        }

        if (_changeCount >= MAX_CHANGES)
        {
            _changeCount = 0;
            return;
        }

        _changeBuffer[_changeCount++] = duration;
        _lastTime = time;
    }

    // Process the received data buffer
    void IRAM_ATTR processReceivedData()
    {
        if (_changeCount < 6)
            return; // Too short to be valid

        // Look for sync pattern
        unsigned long syncLength = _changeBuffer[0];
        if (!isValidSync(syncLength))
            return;

        // Decode the data bits
        unsigned long code = 0;
        unsigned int bitLength = 0;

        for (uint8_t i = 1; i < _changeCount - 1; i += 2)
        {
            if (i + 1 >= _changeCount)
                break;

            unsigned long highTime = _changeBuffer[i];
            unsigned long lowTime = _changeBuffer[i + 1];

            if (isValidBit(highTime, lowTime))
            {
                code <<= 1;
                if (isLogicOne(highTime, lowTime))
                {
                    code |= 1;
                }
                bitLength++;
            }
            else
            {
                return; // Invalid bit timing
            }
        }

        // Valid code received
        if (bitLength >= 8)
        {
            _receivedCode = code;
            _receivedBitLength = bitLength;
            _dataReceived = true;
        }
    }

    // Check if timing matches sync pattern
    bool IRAM_ATTR isValidSync(unsigned long duration)
    {
        return (duration > SYNC_LENGTH * (100 - TOLERANCE) / 100) &&
               (duration < SYNC_LENGTH * (100 + TOLERANCE) / 100);
    }

    // Check if timing is valid for a data bit
    bool IRAM_ATTR isValidBit(unsigned long highTime, unsigned long lowTime)
    {
        unsigned long totalTime = highTime + lowTime;
        unsigned long expectedTime = PULSE_LENGTH * 2;

        return (totalTime > expectedTime * (100 - TOLERANCE) / 100) &&
               (totalTime < expectedTime * (100 + TOLERANCE) / 100);
    }

    // Determine if bit is logic 1 or 0
    bool IRAM_ATTR isLogicOne(unsigned long highTime, unsigned long lowTime)
    {
        // Logic 1: long high, short low
        // Logic 0: short high, long low
        return highTime > lowTime;
    }

public:
    RXB22RFReceiver()
        : _dataPin(RF_RECEIVER_PIN), _initialized(false), _changeCount(0),
          _lastTime(0), _dataReceived(false), _receivedCode(0), _receivedBitLength(0), _receiveCallback(nullptr)
    {
        _instance = this;
        Serial.print(DEVICE_TYPE);
        Serial.println(" RXB22 RF Receiver constructor");
    }

    // Initialize the RF receiver
    bool begin()
    {
        Serial.print(DEVICE_TYPE);
        Serial.println(" Initializing RXB22 RF receiver...");

        // Configure data pin as input with pullup
        pinMode(_dataPin, INPUT_PULLUP);

        // Attach interrupt for data reception
        attachInterrupt(digitalPinToInterrupt(_dataPin), handleInterrupt, CHANGE);

        _initialized = true;

        Serial.print(DEVICE_TYPE);
        Serial.println(" RXB22 RF receiver initialized successfully");
        Serial.print("Data Pin: ");
        Serial.println(_dataPin);

        return true;
    }

    // Set callback function for received data
    void setReceiveCallback(void (*callback)(unsigned long code, unsigned int bitLength))
    {
        _receiveCallback = callback;
        Serial.print(DEVICE_TYPE);
        Serial.println(" RF receive callback set");
    }

    // Check if receiver is initialized
    bool isInitialized()
    {
        return _initialized;
    }

    // Enable/disable receiver
    void enable()
    {
        if (_initialized)
        {
            attachInterrupt(digitalPinToInterrupt(_dataPin), handleInterrupt, CHANGE);
            Serial.print(DEVICE_TYPE);
            Serial.println(" RF receiver enabled");
        }
    }

    void disable()
    {
        if (_initialized)
        {
            detachInterrupt(digitalPinToInterrupt(_dataPin));
            Serial.print(DEVICE_TYPE);
            Serial.println(" RF receiver disabled");
        }
    }

    // Get signal strength (basic implementation)
    int getSignalStrength()
    {
        if (!_initialized)
            return 0;

        // Simple signal strength based on recent activity
        unsigned long now = millis();
        if (now - _lastTime < 1000)
        {
            return 100; // Strong signal (recent activity)
        }
        else if (now - _lastTime < 5000)
        {
            return 50; // Medium signal
        }
        else
        {
            return 0; // No signal
        }
    }

    // Reset receiver state
    void reset()
    {
        if (_initialized)
        {
            _changeCount = 0;
            _dataReceived = false;
            Serial.print(DEVICE_TYPE);
            Serial.println(" RF receiver reset");
        }
    }

    // Get receiver status
    String getStatus()
    {
        if (!_initialized)
        {
            return "Not initialized";
        }

        String status = "Pin: " + String(_dataPin);
        status += ", Signal: " + String(getSignalStrength()) + "%";
        status += ", Changes: " + String(_changeCount);

        return status;
    }

    // Test function to simulate received code (for debugging)
    void simulateReceive(unsigned long code, unsigned int bitLength)
    {
        if (_receiveCallback)
        {
            Serial.print(DEVICE_TYPE);
            Serial.print(" Simulating RF code: 0x");
            Serial.print(code, HEX);
            Serial.print(" (");
            Serial.print(bitLength);
            Serial.println(" bits)");

            _receiveCallback(code, bitLength);
        }
    }

    // Process any received data (call this from main loop)
    void processReceivedDataCallback()
    {
        if (_dataReceived && _receiveCallback)
        {
            _dataReceived = false; // Clear flag first to avoid race condition
            _receiveCallback(_receivedCode, _receivedBitLength);
        }
    }

    // Destructor
    ~RXB22RFReceiver()
    {
        if (_initialized)
        {
            detachInterrupt(digitalPinToInterrupt(_dataPin));
        }
        _instance = nullptr;
    }
};

// Static instance pointer
RXB22RFReceiver *RXB22RFReceiver::_instance = nullptr;

#endif // COMPILE_RF_RECEIVER

#endif // RXB22_RF_RECEIVER_H
