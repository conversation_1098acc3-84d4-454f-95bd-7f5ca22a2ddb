#ifndef DIRECT_PIN_MANAGER_H
#define DIRECT_PIN_MANAGER_H

#include "DeviceConfig.h"

// Only compile this manager for devices that don't use shift registers
#if !USE_SHIFT_REGISTERS

class DirectPinManager
{
private:
    // Pin arrays from configuration
    const uint8_t _relayPins[MAX_SWITCHES] = RELAY_PINS;
    const uint8_t _redPins[MAX_SWITCHES] = RGB_RED_PINS;
    const uint8_t _greenPins[MAX_SWITCHES] = RGB_GREEN_PINS;
    const uint8_t _bluePins[MAX_SWITCHES] = RGB_BLUE_PINS;

    uint8_t _switchCount;

#if HAS_FULL_COLOR_RGB
    void initializePWM()
    {
        // Configure PWM for ESP32 (newer API uses pin-based control)
        for (int i = 0; i < _switchCount; i++)
        {
            // Attach PWM to RGB pins
            ledcAttach(_redPins[i], PWM_FREQUENCY, PWM_RESOLUTION);
            ledcAttach(_greenPins[i], PWM_FREQUENCY, PWM_RESOLUTION);
            ledcAttach(_bluePins[i], PWM_FREQUENCY, PWM_RESOLUTION);

            Serial.print(DEVICE_TYPE);
            Serial.print(" PWM RGB for Switch ");
            Serial.print(i + 1);
            Serial.print(" - R:");
            Serial.print(_redPins[i]);
            Serial.print(" G:");
            Serial.print(_greenPins[i]);
            Serial.print(" B:");
            Serial.println(_bluePins[i]);
        }
    }
#endif

public:
    DirectPinManager() : _switchCount(SWITCH_COUNT)
    {
        Serial.print(DEVICE_TYPE);
        Serial.println(" DirectPinManager constructor");
    }

    // Initialize all pins
    void begin()
    {
        Serial.print(DEVICE_TYPE);
        Serial.println(" DirectPinManager initializing...");

        // Initialize relay pins
#if HAS_RELAYS
        for (int i = 0; i < _switchCount; i++)
        {
            pinMode(_relayPins[i], OUTPUT);
            digitalWrite(_relayPins[i], LOW); // Start with relays OFF

            Serial.print(DEVICE_TYPE);
            Serial.print(" Relay ");
            Serial.print(i + 1);
            Serial.print(" initialized on pin ");
            Serial.println(_relayPins[i]);
        }
#endif

#if HAS_FULL_COLOR_RGB
        // Initialize PWM for full color RGB (ESP32)
        initializePWM();
#else
        // Initialize digital RGB pins (basic on/off control)
        for (int i = 0; i < _switchCount; i++)
        {
            pinMode(_redPins[i], OUTPUT);
            pinMode(_greenPins[i], OUTPUT);
            pinMode(_bluePins[i], OUTPUT);

            // Set initial RGB state to OFF
            digitalWrite(_redPins[i], LOW);
            digitalWrite(_greenPins[i], LOW);
            digitalWrite(_bluePins[i], LOW);

            Serial.print(DEVICE_TYPE);
            Serial.print(" Digital RGB for Switch ");
            Serial.print(i + 1);
            Serial.print(" - R:");
            Serial.print(_redPins[i]);
            Serial.print(" G:");
            Serial.print(_greenPins[i]);
            Serial.print(" B:");
            Serial.println(_bluePins[i]);
        }
#endif

        Serial.print(DEVICE_TYPE);
        Serial.println(" DirectPinManager initialized");
    }

    // Set relay state (only for devices with relays)
    void setRelay(uint8_t relayIndex, bool state)
    {
#if HAS_RELAYS
        if (relayIndex >= _switchCount)
            return;

        digitalWrite(_relayPins[relayIndex], state);

        Serial.print(DEVICE_TYPE);
        Serial.print(" Relay ");
        Serial.print(relayIndex + 1);
        Serial.print(" set to ");
        Serial.println(state ? "ON" : "OFF");
#endif
    }

    // Get relay state
    bool getRelay(uint8_t relayIndex)
    {
#if HAS_RELAYS
        if (relayIndex >= _switchCount)
            return false;

        return digitalRead(_relayPins[relayIndex]);
#else
        return false; // No relays on this device model
#endif
    }

    // Set RGB color
    void setRGB(uint8_t switchIndex, uint8_t r, uint8_t g, uint8_t b)
    {
        if (switchIndex >= _switchCount)
            return;

#if HAS_FULL_COLOR_RGB
        // ESP32 PWM control (0-255 range)
        ledcWrite(_redPins[switchIndex], r);
        ledcWrite(_greenPins[switchIndex], g);
        ledcWrite(_bluePins[switchIndex], b);

        Serial.print(DEVICE_TYPE);
        Serial.print(" PWM RGB for Switch ");
        Serial.print(switchIndex + 1);
        Serial.print(" set to R:");
        Serial.print(r);
        Serial.print(" G:");
        Serial.print(g);
        Serial.print(" B:");
        Serial.println(b);
#else
        // Digital RGB control (on/off only)
        digitalWrite(_redPins[switchIndex], r > 0);
        digitalWrite(_greenPins[switchIndex], g > 0);
        digitalWrite(_bluePins[switchIndex], b > 0);

        Serial.print(DEVICE_TYPE);
        Serial.print(" Digital RGB for Switch ");
        Serial.print(switchIndex + 1);
        Serial.print(" set to R:");
        Serial.print(r > 0 ? "ON" : "OFF");
        Serial.print(" G:");
        Serial.print(g > 0 ? "ON" : "OFF");
        Serial.print(" B:");
        Serial.println(b > 0 ? "ON" : "OFF");
#endif
    }

    // Get RGB color
    void getRGB(uint8_t switchIndex, uint8_t &r, uint8_t &g, uint8_t &b)
    {
        if (switchIndex >= _switchCount)
        {
            r = g = b = 0;
            return;
        }

#if HAS_FULL_COLOR_RGB
        // For PWM, we'd need to track the values since we can't read them back
        // This would require storing the current values in member variables
        // For now, return 0 or 255 based on digital read
        r = digitalRead(_redPins[switchIndex]) ? 255 : 0;
        g = digitalRead(_greenPins[switchIndex]) ? 255 : 0;
        b = digitalRead(_bluePins[switchIndex]) ? 255 : 0;
#else
        // Digital RGB - return 0 or 255
        r = digitalRead(_redPins[switchIndex]) ? 255 : 0;
        g = digitalRead(_greenPins[switchIndex]) ? 255 : 0;
        b = digitalRead(_bluePins[switchIndex]) ? 255 : 0;
#endif
    }

    // Clear all outputs
    void clearAll()
    {
#if HAS_RELAYS
        for (int i = 0; i < _switchCount; i++)
        {
            digitalWrite(_relayPins[i], LOW);
        }
#endif

        for (int i = 0; i < _switchCount; i++)
        {
#if HAS_FULL_COLOR_RGB
            ledcWrite(_redPins[i], 0);
            ledcWrite(_greenPins[i], 0);
            ledcWrite(_bluePins[i], 0);
#else
            digitalWrite(_redPins[i], LOW);
            digitalWrite(_greenPins[i], LOW);
            digitalWrite(_bluePins[i], LOW);
#endif
        }

        Serial.print("All ");
        Serial.print(DEVICE_TYPE);
        Serial.println(" direct pin outputs cleared");
    }
};

#endif // !USE_SHIFT_REGISTERS

#endif // DIRECT_PIN_MANAGER_H
